package ip;

import com.jackrain.nea.R3SrvDubboApplication;
import com.jackrain.nea.ip.api.vips.VipBillDownloadServiceCmd;
import com.jackrain.nea.ip.model.vips.VipBillOrderGetModel;
import com.jackrain.nea.web.face.User;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {R3SrvDubboApplication.class})
public class TestVipBillTest {

    @Autowired
    VipBillDownloadServiceCmd downloadServiceCmd;


    @Test
    public void TestVipBillTest(){
        VipBillOrderGetModel getModel = new VipBillOrderGetModel();
        getModel.setIsMonth("0");
        getModel.setShopId("11");
        getModel.setStartDate("2020-07-10 01:00:00");
        getModel.setEndDate("2020-07-11 01:00:00");
        getModel.setUser(new User() {
            @Override
            public boolean isGuest() {
                return false;
            }

            @Override
            public int getMemberId() {
                return 0;
            }

            @Override
            public String getTruename() {
                return null;
            }

            @Override
            public int getClientId() {
                return 0;
            }

            @Override
            public int getStoreId() {
                return 0;
            }

            @Override
            public void setStoreId(int storeId) {

            }

            @Override
            public String getStoreName() {
                return null;
            }

            @Override
            public void setStoreName(String storeName) {

            }

            @Override
            public int getOrgId() {
                return 0;
            }

            @Override
            public Locale getLocale() {
                return null;
            }

            @Override
            public String getEmail() {
                return null;
            }

            @Override
            public String getPhone() {
                return null;
            }

            @Override
            public void setPhone(String phone) {

            }

            @Override
            public Integer getId() {
                return null;
            }

            @Override
            public String getNameWithDomain() {
                return null;
            }

            @Override
            public String getClientDomainName() {
                return null;
            }

            @Override
            public String getName() {
                return null;
            }

            @Override
            public String getEname() {
                return null;
            }

            @Override
            public String getPasswordHash() {
                return null;
            }

            @Override
            public String getPwdrand() {
                return null;
            }

            @Override
            public int getIsEnabled() {
                return 0;
            }

            @Override
            public int getIsEmployee() {
                return 0;
            }

            @Override
            public boolean isAdmin() {
                return false;
            }

            @Override
            public boolean isDev() {
                return false;
            }

            @Override
            public String getDescription() {
                return null;
            }

            @Override
            public boolean isActive() {
                return false;
            }

            @Override
            public int getSecurityGrade() {
                return 0;
            }

            @Override
            public long getPassordExpireDateTime() {
                return 0;
            }

            @Override
            public boolean isPasswordExpired() {
                return false;
            }

            @Override
            public boolean isValidPassword(String paramString) {
                return false;
            }

            @Override
            public int getFailedLoginAttempts() {
                return 0;
            }

            @Override
            public Date getLastlogindate() {
                return null;
            }

            @Override
            public String getLastloginip() {
                return null;
            }

            @Override
            public String getHeadImg() {
                return null;
            }

            @Override
            public void setHeadmg(String headImg) {

            }

            @Override
            public String getClientName() {
                return null;
            }

            @Override
            public void setClientName(String clientName) {

            }

            @Override
            public int getSupClientId() {
                return 0;
            }

            @Override
            public void setSupClientId(int supClientId) {

            }

            @Override
            public HashMap getUserenv() {
                return null;
            }

            @Override
            public void setUserenv(HashMap userenv) {

            }

            @Override
            public String getLoginIPRule() {
                return null;
            }

            @Override
            public void setLoginIPRule(String loginIPRule) {

            }

            @Override
            public List getGroups() {
                return null;
            }

            @Override
            public Integer getCpCstoreId() {
                return null;
            }

            @Override
            public void setCpCstoreId(Integer cpCstoreId) {

            }

            @Override
            public Integer getCpCsupplierId() {
                return null;
            }

            @Override
            public void setCpCsupplierId(Integer cpCsupplierId) {

            }
        });
        getModel.setLocale(null);
        getModel.setVendorCode("10001");
        downloadServiceCmd.downloadVipBill(getModel);
    }



}
