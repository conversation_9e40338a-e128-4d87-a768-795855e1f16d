FROM --platform=linux/amd64 registry.cn-hangzhou.aliyuncs.com/ryytn-r3-uat/openjdk:8u351

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

ENV LOCAL_PATH /acs/user/src
ENV APP_NAME r3-ip-srv-shell-dubbo-3.0.0-SNAPSHOT

COPY R3-IP-SRV-SHELL-DUBBO/target/${APP_NAME}.jar ${LOCAL_PATH}/${APP_NAME}.jar
COPY R3-IP-SRV-SHELL-DUBBO/src/main/docker/entrypoint.sh ${LOCAL_PATH}/entrypoint.sh

WORKDIR /acs/user/src

#RUN echo 'alias log="cd /acs/user/src/app/log/"' >> ~/.bashrc

EXPOSE 8080
ENTRYPOINT ["/bin/sh", "entrypoint.sh"]