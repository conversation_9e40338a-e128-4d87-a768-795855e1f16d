<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!--<include resource="org/springframework/boot/logging/logback/base.xml" />-->
	<!--<conversionRule conversionWord="ip" converterClass="com.jackrain.nea.plus.LogIpConfig"/>-->

	<!-- 定义日志文件 输入位置 -->
	<property name="log_dir" value="./app/log"/>
	<!-- 配置日志清理时间，日志最大的历史3天 -->
	<property name="maxHistory" value="3"/>
	<!-- 配置日志文件限制 -->
	<property name="totalSizeCap" value="1GB"/>
	<!-- 设置单个日志文件的大小限制 -->
	<property name="maxFileSize" value="300MB"/>
	<property name="PATTERN"
		value="[%date{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%-5level] [%logger{15}#%line] %msg%n"/>

	<!-- ConsoleAppender 控制台输出日志 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
			<pattern>${PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- Only log level WARN and above -->
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>DEBUG</level>
		</filter>
	</appender>

	<!-- root -->
	<root level="INFO">
		<appender-ref ref="STDOUT"/>
	</root>

	<springProfile name="dev, test, prod">
		<logger name="org.apache.dubbo" level="INFO"/>
		<logger name="com.alibaba.dubbo" level="WARN"/>
		<logger name="org.springframework" level="WARN"/>

		<logger name="com.github.ltsopensource" level="ERROR"/>

		<logger name="RocketmqCommon" level="ERROR"/>
		<logger name="RocketmqRemoting" level="ERROR"/>
		<logger name="RocketmqClient" level="ERROR"/>

		<logger name="com.jackrain.nea.web.common.ObjectSingle" level="OFF"/>
		<logger name="com.jackrain.nea.jdbc.datasource" level="OFF"/>
		<logger name="com.zaxxer.hikari.pool" level="OFF"/>
		<logger name="org.apache.ibatis.session.defaults" level="OFF"/>
		<logger name="com.jackrain.nea.vp.job" level="WARN"/>
		<logger name="com.jackrain.nea.config" level="WARN"/>
		<logger name="com.jackrain" level="DEBUG"/>
		<logger name="com.burgeon" level="DEBUG"/>
	</springProfile>

</configuration>
