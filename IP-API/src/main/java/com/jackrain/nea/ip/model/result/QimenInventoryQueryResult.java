package com.jackrain.nea.ip.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 16:12 2020/3/26
 * description ：奇门库存查询返回model
 * @ Modified By：
 */
@Data
public class QimenInventoryQueryResult implements Serializable {
    private static final long serialVersionUID = -3355971290334175918L;

    /**
     * 库存类型(ZP=正品;CC=残次;JS=机损;XS= 箱损;ZT=在途库存)
     */
    private String inventoryType;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 冻结库存数量
     */
    private BigDecimal lockQuantity;

    /**
     * 商品生产日期(YYYY-MM-DD)
     */
    private String productDate;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 商品SKUID
     */
    private Long skuId;

    /**
     * 逻辑仓ID
     */
    private Long storeId;
}
