package com.jackrain.nea.ip.api.intercept;

import com.jackrain.nea.ip.model.request.intercept.CancelInterceptRequest;
import com.jackrain.nea.ip.model.result.intercept.CancelInterceptResponse;

import java.util.List;

/**
 * @Author: yang<PERSON>i
 * @Date: 2024-10-30 10:23
 */
public interface CancelInterceptCmd {
    List<CancelInterceptResponse> cancelIntercept(List<CancelInterceptRequest> requests);
}
