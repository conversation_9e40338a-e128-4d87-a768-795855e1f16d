package com.jackrain.nea.ip.model.others;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 19:17 2020/8/17
 * description ：
 * @ Modified By：
 */
@Data
public class YangYiConfirmRequest implements Serializable {
    private static final long serialVersionUID = -5439002690986773006L;

    /**
     * 单据ID
     */
    private Long orderId;

    /**
     * 来源单据编号
     */
    private String djbh;

    /**
     * 业务日期
     */
    private Date rq;

    /**
     * 物流备注
     */
    private String locremark;

    /**
     * 商品明细
     */
    private List<Item> items;

    @Data
    public static class Item implements Serializable {

        private static final long serialVersionUID = -8910252545169842681L;

        /**
         * 条码
         */
        private String sku;

        /**
         * 数量
         */
        private BigDecimal qty;
    }


}
