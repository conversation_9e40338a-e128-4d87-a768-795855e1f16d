package com.jackrain.nea.ip.api.others;

import com.jackrain.nea.ip.model.others.StandplatLogisticsSendDataModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import java.util.List;

/**
 * <AUTHOR>
 * date ：Created in 2020-07-13
 * description ：
 * @ Modified By：
 */
public interface StandplatLogisticsSendCmd {
    ValueHolderV14<List<StandplatLogisticsSendResult>> sendStandplatLogistics(StandplatLogisticsSendDataModel logisticsSendDataModel);

    ValueHolderV14  sendStandplatLogisticsExchange(OcBOrder ocBOrder,String companyCode,String returnNo);
}
