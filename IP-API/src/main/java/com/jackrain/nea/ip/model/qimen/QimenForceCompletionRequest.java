package com.jackrain.nea.ip.model.qimen;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * description ：单据强制完成接口（WMS->中台）request
 */
@Data
public class QimenForceCompletionRequest implements Serializable {

    private static final long serialVersionUID = -8049951105558278627L;
    /**
     * 业务单据编号
     */
    private String billNo;
    /**
     * 单据类型
     */
    private String billType;
    /**
     * 操作人
     */
    private String ownerName;

    private List<OrderItem> orderItems;


    @Data
    public static class OrderItem implements Serializable {
        private static final long serialVersionUID = -738731646563011130L;
        /**
         * 条码
         */
        private String skuCode;
        /**
         * 出库数量
         */
        private BigDecimal qtyOut;
        /**
         * 入库数量
         */
        private BigDecimal qtyIn;
    }


}
