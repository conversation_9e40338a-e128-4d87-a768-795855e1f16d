package com.jackrain.nea.ip.api.bn;

import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;
import java.util.Map;

/**
 * @ClassName BnCmd
 * @Description 班牛对接接口
 * <AUTHOR>
 * @Date 2024/11/13 09:24
 * @Version 1.0
 */
public interface BnCmd {

    /**
     * 工作表组件
     *
     * @param projectId
     * @return
     */
    ValueHolderV14 queryColumnList(Long projectId);

    /**
     * 创建工单
     *
     * @param content   为map key value的结构
     * @param projectId
     * @param appId
     * @param userId
     * @return
     */
    ValueHolderV14 pushTask(Map<String, String> content, String projectId, String appId, String userId);

    /**
     * 批量创建工单
     *
     * @param contents  为map key value的结构
     * @param projectId
     * @param appId
     * @param userId
     * @return
     */
    ValueHolderV14 batchPushTask(List<Map<String, String>> contents, String projectId, String appId, String userId);

    /**
     * 上传文件
     *
     * @param url  文件路径
     * @param name 文件名称
     * @param type 文件类型
     * @return
     */
    ValueHolderV14 uploadFile(String url, String name, String type);

}
