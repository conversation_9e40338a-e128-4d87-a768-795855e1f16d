package com.jackrain.nea.ip.model.result.jt;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/4/21 16:00
 * @Description TODO
 * @Version 1.0
 */
@Data
public class JtInterceptReceiveRequest  implements Serializable {

    /**
     * 通知拦截结果的运单号
     */
    private String mailNo;
    /**
     * 拦截结果，取值如下 ：
     * "success"：代表拦截成功，
     *
     * "fail"：代表拦截失败，
     *
     * "processing"：代表拦截进⾏中
     */
    private String interceptResult;
    /**
     * 拦截成功后，产⽣的退回单号（仅拦截成功状态时回传）
     */
    private String returnMailNo;
    /**
     * 拦截失败原因（仅拦截失败状态时回传）
     */
    private String errorDesc;

}
