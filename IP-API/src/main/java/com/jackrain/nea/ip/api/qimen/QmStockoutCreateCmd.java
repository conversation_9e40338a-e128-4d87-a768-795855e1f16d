package com.jackrain.nea.ip.api.qimen;

import com.jackrain.nea.ip.model.qimen.QmStockoutCreateModel;
import com.jackrain.nea.ip.model.result.QimenAsyncMqResult;
import com.jackrain.nea.ip.model.result.QimenOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import java.util.List;

/**
 * 奇门出库单创建接口
 *
 * @author: 李杰
 * @since: 2019/5/7
 * create at : 2019/5/7 8:33
 */
public interface QmStockoutCreateCmd {
    /**
     * 异步接口
     *
     * @param models
     * @param centerName
     * @param user
     * @return
     */
    ValueHolderV14<List<QimenAsyncMqResult>> stockoutCreate(List<QmStockoutCreateModel> models, String centerName, User user);

    /**
     * 同步接口
     *
     * @param model
     * @param user
     * @return
     */
    ValueHolderV14 syncStockoutCreate(QmStockoutCreateModel model, User user);

    /**
     * 单体奇门接口调用
     * @param model
     * @return
     */
    ValueHolderV14<QimenOrderResult> sysStockoutCreate(QmStockoutCreateModel model);

}
