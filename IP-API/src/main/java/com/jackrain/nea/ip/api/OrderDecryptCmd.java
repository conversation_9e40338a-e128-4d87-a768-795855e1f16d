package com.jackrain.nea.ip.api;

import com.jackrain.nea.ip.model.request.OrderDecryptRequest;
import com.jackrain.nea.ip.model.result.OrderDecryptResponse;

/**
 * className: OrderDecryptCmd
 * description: 零售发货单信息解密
 *
 * <AUTHOR>
 * create: 2021-08-03
 * @since JDK 1.8
 */
public interface OrderDecryptCmd {
    /**
     * 订单信息解密
     * @param decryptRequest 解密参数
     * @return 解密结果
     */
    OrderDecryptResponse orderDecrypt(OrderDecryptRequest decryptRequest);
}
