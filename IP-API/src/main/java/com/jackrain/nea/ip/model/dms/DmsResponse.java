package com.jackrain.nea.ip.model.dms;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName DmsResponse
 * @Description dms返回实体类
 * <AUTHOR>
 * @Date 2024/6/20 10:29
 * @Version 1.0
 */
@Data
public class DmsResponse implements Serializable {
    private static final long serialVersionUID = -877911091349350563L;

    /**
     * 表示操作是否成功的标志。
     */
    private boolean success;

    /**
     * 操作结果的描述信息。
     */
    private String message;

    /**
     * 响应代码，200通常表示成功。
     */
    private int code;

    /**
     * 操作结果数据，此处为null表示无具体数据返回。
     */
    private Object result;

    /**
     * 响应时间戳，单位为毫秒。
     */
    private Date timestamp;
}
