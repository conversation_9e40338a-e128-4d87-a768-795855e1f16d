package com.jackrain.nea.ip.api.qimen;

import com.jackrain.nea.ip.model.qimen.QimenEntryorderCreateModel;
import com.jackrain.nea.ip.model.result.QimenAsyncMqResult;
import com.jackrain.nea.ip.model.result.QimenOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import java.util.List;

/**
 * <AUTHOR>
 * date ：Created in 14:19 2019/4/18
 * description ：奇门入库单创建接口
 * @ Modified By：
 */
public interface QimenEntryorderCreateCmd {
    ValueHolderV14 createQimenEntryOrder(QimenEntryorderCreateModel qimenEntryorderCreateModel, User operateUser);

    ValueHolderV14<List<QimenAsyncMqResult>> asyncCreateQimenEntryOrder(List<QimenEntryorderCreateModel> qimenEntryorderCreateModels, User operateUser, String centreName);

    ValueHolderV14<List<QimenAsyncMqResult>> asyncCreateQimenEntryOrderSpecial(List<QimenEntryorderCreateModel> qimenEntryorderCreateModels, User operateUser, String centreName);

    ValueHolderV14<QimenOrderResult> sysQimenEntryOrder(QimenEntryorderCreateModel entryorderCreateModelList);


}
