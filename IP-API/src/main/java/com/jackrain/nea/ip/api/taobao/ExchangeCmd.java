package com.jackrain.nea.ip.api.taobao;

import com.jackrain.nea.ip.model.ExchangeAgreeModel;
import com.jackrain.nea.ip.model.ExchangeRefuseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * 卖家换货操作
 *
 * <AUTHOR> 蒋晓宇
 * @since : 2020/7/1
 * create at : 2020/7/1 14:55
 */
public interface ExchangeCmd {
    /**
     * 同意换货
     *
     * @param agreeModel :入参
     * @return
     * <AUTHOR>
     * @since 2020/7/1
     */
    ValueHolderV14 agree(ExchangeAgreeModel agreeModel);

    /**
     * 拒绝换货
     *
     * @param refuseModel :入参
     * @return
     * <AUTHOR>
     * @since 2020/7/1
     */
    ValueHolderV14 refuse(ExchangeRefuseModel refuseModel);
}
