package com.jackrain.nea.ip.model.wing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel("定金预售传wing发货接口")
public class WingDeliveryItemModel implements Serializable {
    @ApiModelProperty("中台单号")
    private String ztId;

    @ApiModelProperty("平台单号")
    private String tid;
    
    @ApiModelProperty("新版SKU")
    private String sku;
    
    @ApiModelProperty("成交单价")
    private String price;
}
