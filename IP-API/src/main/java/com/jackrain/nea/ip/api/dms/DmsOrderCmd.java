package com.jackrain.nea.ip.api.dms;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ip.model.dms.request.DmsOrderAddRequest;
import com.jackrain.nea.ip.model.dms.request.DmsRefundAddRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName DmsOrderCmd
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/1 17:10
 * @Version 1.0
 */
public interface DmsOrderCmd {
    /**
     * 保存标准订单
     *
     * @param param
     * @return
     */
    ValueHolderV14 saveStandPlayOrder(DmsOrderAddRequest dmsOrderAddRequest);

    /**
     * 单据取消 订单、售后单
     *
     * @param param
     * @return
     */
    ValueHolderV14 cancelOrder(JSONObject param);

    ValueHolderV14 queryOrder(JSONObject param);

    /**
     * 售后创建
     *
     * @param dmsRefundAddRequest
     * @return
     */
    ValueHolderV14 saveRefundOrder(DmsRefundAddRequest dmsRefundAddRequest);

    /**
     * 贴标要求查询
     *
     * @param param
     * @return
     */
    ValueHolderV14 queryLabelingRequirements();
}
