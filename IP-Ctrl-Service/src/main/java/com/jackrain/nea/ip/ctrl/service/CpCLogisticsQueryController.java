package com.jackrain.nea.ip.ctrl.service;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpLogisticsSelectServiceCmd;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2020-08-12
 * @desc 店铺查询
 **/
@RestController
@Api(value = "CpCLogisticsQueryController", description = "物流查询")
public class CpCLogisticsQueryController {

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpLogisticsSelectServiceCmd cpLogisticsSelectServiceCmd;

    /**
     * 物流列表查询
     *
     * @return
     */
    @RequestMapping(path = "api/ip/wos/v1/queryAllLogisticsList", method = {RequestMethod.POST, RequestMethod.GET})
    @ApiOperation(value = "物流列表查询")
    public JSONObject logisticsList() {
        ValueHolderV14<List<CpLogistics>> vh;
        try {
            vh = cpLogisticsSelectServiceCmd.queryLogisticsIsY();
            if (vh == null) {
                throw new NDSException("查询结果为Null");
            }
        } catch (Exception e) {
            vh = new ValueHolderV14();
            vh.setMessage(e.getMessage());
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        return vh.toJSONObject();
    }
}
