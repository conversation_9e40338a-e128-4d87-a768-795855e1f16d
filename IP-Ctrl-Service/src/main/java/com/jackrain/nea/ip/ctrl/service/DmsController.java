package com.jackrain.nea.ip.ctrl.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ip.api.dms.DmsConfirmCmd;
import com.jackrain.nea.ip.api.dms.DmsOrderCmd;
import com.jackrain.nea.ip.manager.IpBThirdPartyInterfaceLogManager;
import com.jackrain.nea.ip.model.dms.request.DmsOrderAddRequest;
import com.jackrain.nea.ip.model.dms.request.DmsRefundAddRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;

/**
 * @ClassName DmsController
 * @Description DMS 接口
 * <AUTHOR>
 * @Date 2024/5/31 18:07
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/ip/dms")
public class DmsController {

    @Autowired
    private DmsOrderCmd dmsOrderCmd;
    @Autowired
    private DmsConfirmCmd dmsConfirmCmd;
    @Autowired
    private IpBThirdPartyInterfaceLogManager thirdPartyInterfaceLogManager;

    @ApiOperation(value = "DMS订单下推")
    @PostMapping(path = "/order/save")
    public ValueHolderV14 saveOrder(@RequestBody @Valid DmsOrderAddRequest dmsOrderAddRequest) {
        ValueHolderV14 valueHolderV14 = dmsOrderCmd.saveStandPlayOrder(dmsOrderAddRequest);
        String varchar02 = JSON.toJSONString(valueHolderV14);
        thirdPartyInterfaceLogManager.insertThirdInterfaceLog(dmsOrderAddRequest.getTid(), 101L, "dmsOrderSave",
                "中台", "DMS", valueHolderV14.getCode(), new Date(), JSONUtil.toJsonStr(dmsOrderAddRequest), varchar02, "DMS");
        return valueHolderV14;
    }

    @ApiOperation(value = "DMS订单/售后取消")
    @PostMapping(path = "/order/cancel")
    public ValueHolderV14 cancelOrder(@RequestBody JSONObject param) {
        return dmsOrderCmd.cancelOrder(param);
    }


    @ApiOperation(value = "DMS订单查询")
    @PostMapping(path = "/order/query")
    public ValueHolderV14 queryOrder(@RequestBody JSONObject param) {
        log.info("DmsController queryOrder param:{}", param);
        return dmsOrderCmd.queryOrder(param);
    }

    @ApiOperation(value = "DMS售后创建")
    @PostMapping(path = "/refund/save")
    public ValueHolderV14 saveRefundOrder(@RequestBody @Valid DmsRefundAddRequest dmsRefundAddRequest) {
        ValueHolderV14 valueHolderV14 = dmsOrderCmd.saveRefundOrder(dmsRefundAddRequest);
        String varchar02 = JSON.toJSONString(valueHolderV14);
        thirdPartyInterfaceLogManager.insertThirdInterfaceLog(dmsRefundAddRequest.getTid(), 201L, "dmsRefundSave",
                "中台", "DMS", valueHolderV14.getCode(), new Date(), JSONUtil.toJsonStr(dmsRefundAddRequest), varchar02, "DMS");
        return valueHolderV14;
    }

    @ApiOperation(value = "增值服务档案")
    @GetMapping(path = "/labelingRequirements/query")
    public ValueHolderV14 queryLabelingRequirements() {
        return dmsOrderCmd.queryLabelingRequirements();
    }


    //--------------------财务------------------------

    @ApiOperation(value = "未对账金额查询（实时）")
    @PostMapping(path = "/confirm/no/amt")
    public ValueHolderV14 queryConfirmNoAmt(@RequestBody JSONObject param) {
        log.info("DmsController queryConfirmNoAmt param:{}", param);
        return dmsConfirmCmd.noConfirmData(param);
    }

    @ApiOperation(value = "月未对账数据查询")
    @PostMapping(path = "/confirm/no/data")
    public ValueHolderV14 queryConfirmNoData(@RequestBody JSONObject param) {
        log.info("DmsController queryConfirmNoData param:{}", param);
        return dmsConfirmCmd.queryConfirmNoData(param);
    }

    //--------------------财务------------------------

}
