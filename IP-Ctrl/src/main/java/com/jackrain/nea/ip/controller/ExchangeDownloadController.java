package com.jackrain.nea.ip.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.ip.api.ExchangeDownloadCmd;
import com.jackrain.nea.ip.services.ExchangeDownloadCmdImpl;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * date ：Created in 19:02 2019/4/9
 * description ：换货单下载controller
 * @ Modified By：
 */
@RestController
@Slf4j
@Api(value = "ExchangeDownloadController", description = "换货单下载接口")
public class ExchangeDownloadController {
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "换货单下载")
    @RequestMapping(path = "/api/cs/ip/exchangeDownload", method = {RequestMethod.POST, RequestMethod.GET})
    @TargetDataSource(name = "mysql")
    public JSONObject exchangeDownload(HttpServletRequest request, @RequestParam("param") String param) {
        if (log.isDebugEnabled()) {
            log.debug("Start exchangeDownload. ReceiveParams=" + param + ";");
        }
        ValueHolder vh = new ValueHolder();
        try {
            User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
            QuerySession querySession = QueryUtils.createQuerySession(user);
//            QuerySessionImpl querySession = new QuerySessionImpl(request);
            DefaultWebEvent event = new DefaultWebEvent("test", request, false);
            event.put("param", JSON.parseObject(param));
            querySession.setEvent(event);
            Object o = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), ExchangeDownloadCmd.class.getName(), "ip", "1.4.0");
            ExchangeDownloadCmdImpl bean = ApplicationContextHandle.getBean(ExchangeDownloadCmdImpl.class);
            vh = bean.execute(querySession);
        } catch (Exception e) {
            e.printStackTrace();
            vh.put("code", -1);
            vh.put("message", Resources.getMessage(e.getMessage()));
        }
        if (log.isDebugEnabled()) {
            log.debug("Finish exchangeDownload. Return Result=" + vh.toJSONObject().toString());
        }
        return vh.toJSONObject();
    }
}
