package com.jackrain.nea.ip.mapper;

import com.jackrain.nea.ip.model.CpCStore;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CpCStoreMapper extends ExtentionMapper<CpCStore> {

    @Select("select count(1) from cp_c_store where ecode=#{ecode}")
    int storeEcodeCount(@Param("ecode") String ecode);

    @Select("select id from cp_c_store where ecode=#{ecode}")
    Long storeId(@Param("ecode") String ecode);

    @Select("select count(1) from cp_c_phy_warehouse where ecode=#{ecode}")
    int phyEcodeCount(@Param("ecode") String ecode);

    @Select("select id from cp_c_phy_warehouse where ecode=#{ecode}")
    Long phyWarehouseId(@Param("ecode") String ecode);
}