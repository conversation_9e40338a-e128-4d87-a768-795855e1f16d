package com.jackrain.nea.ip.service.sap;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.enums.OcOrderRefundStatusEnum;
import com.jackrain.nea.ip.enums.RefundTypeEnum;
import com.jackrain.nea.ip.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.ip.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.ip.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.ip.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.ip.mapper.OcBOrderMapper;
import com.jackrain.nea.ip.model.MqModel;
import com.jackrain.nea.ip.model.sap.SapPushB2bOrderRequest;
import com.jackrain.nea.ip.model.sap.SapRejectRefundRequest;
import com.jackrain.nea.ip.rpc.CpRpcExtService;
import com.jackrain.nea.ip.rpc.PsCoreRpcService;
import com.jackrain.nea.ip.rpc.RpcOmsFiService;
import com.jackrain.nea.ip.service.RegionNewService;
import com.jackrain.nea.ip.utill.IpMqSendUtil;
import com.jackrain.nea.ip.utill.OperatorUtils;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * @title: 采购单下发/取消
 * <AUTHOR>
 * @Date: 2022/6/23 10:34
 * @Version 1.0
 */
@Service
@Slf4j
public class SapOrderCommonService {

    @Autowired
    private IpMqSendUtil r3SendMqUtil;

    @Autowired
    private CpRpcExtService cpRpcExtService;

    @Autowired
    private PsCoreRpcService psCoreRpcService;

    @Autowired
    private IpBStandplatOrderMapper standOrderMapper;
    @Autowired
    private IpBStandplatOrderItemMapper standOrderItemMapper;

    @Autowired
    private IpBStandplatRefundMapper refundOrderMapper;

    @Autowired
    private IpBStandplatRefundItemMapper refundOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private RpcOmsFiService rpcOmsFiService;
    @Autowired
    private RegionNewService regionService;

    private static final String STAND_MASTER_TABLE_NAME = "ip_b_standplat_order";
    private static final String STAND_CHILD_TABLE_NAME = "ip_b_standplat_order_item";
    private static final String STAND_REFUND_MASTER_TABLE_NAME = "ip_b_standplat_refund";
    private static final String STAND_REFUND_CHILD_TABLE_NAME = "ip_b_standplat_refund_item";

    private static final Integer EXPIRY_DATE_TYPE01 = 1;
    //SAP平台编码
    private static final String PLATFORM_CODE = "38";

    public ValueHolderV14 orderHandle(JSONObject params)  {
        SapPushB2bOrderRequest sapOrderRequest = JSONObject.parseObject(params.toString(),
                SapPushB2bOrderRequest.class);
        if (sapOrderRequest == null) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常");
        }

        ValueHolderV14 v14 = this.validate(sapOrderRequest, false);
        if (v14 != null) {
            return v14;
        }

        // 退单的店铺信息要根据退单上的平台单号去找正向订单,以此来获取店铺信息
        CpShop cpShop = this.getShopInfo(sapOrderRequest.getShopCode());

        IpBStandplatOrder exist = standOrderMapper.exist(sapOrderRequest.getTid(), cpShop.getId());

        if (exist != null) {
            return ValueHolderV14Utils.getSuccessValueHolder("该订单已存在，不允许重复新增!");
        }

        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByTid(sapOrderRequest.getTid());
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            return ValueHolderV14Utils.getFailValueHolder("零售发货单已存在，不允许重复新增!");
        }

        IpBStandplatOrder orderRequest = this.buildOrder(sapOrderRequest, cpShop);

        this.setShopInfo(orderRequest, cpShop);

        List<IpBStandplatOrderItem> items = this.buildOrderItems(sapOrderRequest.getItem(), orderRequest.getTid(),
                orderRequest.getId());

        SapOrderCommonService.getInstance().save(orderRequest, items);
        List<MqModel> mqModels = this.buildArr(orderRequest);


        try {
            //发送转单消息
            r3SendMqUtil.sendR3OmsTransferMqInfo(mqModels, ChannelType.STANDPLAT, OperateType.TRANSFER_ORDER,
                    OrderType.NORMAL, UUID.randomUUID().toString());
        } catch (Exception e) {
            log.error(LogUtil.format("SAP订单转单消息发送失败={}",
                    "SapOrderCommonService"), Throwables.getStackTraceAsString(e));
        }
        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "success", mqModels);
    }


    public ValueHolderV14 returnHandle(JSONObject params) {
            /*

        IpBStandplatOrder order = standOrderMapper.existBaiQiu(orderRequest.getOrderCode(), PlatFormEnum.BAI_QIU
        .getLongVal());*/

        SapPushB2bOrderRequest sapOrderRequest = JSONObject.parseObject(params.toString(),
                SapPushB2bOrderRequest.class);
        if (sapOrderRequest == null) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常");
        }

        ValueHolderV14 v14 = this.validate(sapOrderRequest, true);

        if (v14 != null) {
            return v14;
        }

        IpBStandplatRefund exist = refundOrderMapper.exist(sapOrderRequest.getTid());

        if (exist != null) {
            return ValueHolderV14Utils.getSuccessValueHolder("该退单已存在，不允许重复新增！");
        }

        // 退单的店铺信息要根据退单上的平台单号去找正向订单,以此来获取店铺信息
        CpShop cpShop = this.getShopInfo(sapOrderRequest.getShopCode());

        IpBStandplatRefund refund = this.buildStandOrder(sapOrderRequest, cpShop);
        List<IpBStandplatRefundItem> refundItems = this.buildStandOrderItem(sapOrderRequest.getItem(),
                sapOrderRequest.getTid(), "", refund.getId());

        SapOrderCommonService.getInstance().save(refund, refundItems);

        List<MqModel> mqModels = this.buildArr(refund);

        try {
            r3SendMqUtil.sendR3OmsTransferMqInfo(mqModels, ChannelType.STANDPLAT, OperateType.TRANSFER_ORDER,
                    OrderType.REFUND, UUID.randomUUID().toString());
        } catch (Exception e) {
            log.error(LogUtil.format("SAP退单转单消息发送失败={}",
                    "SapOrderCommonService"), Throwables.getStackTraceAsString(e));
        }

        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "success", mqModels);
    }


    /**
     * 拒单 todo 后期优化
     *
     * @param sapOrderRequest
     * @return
     */
    public ValueHolderV14 rejectRefundPush(SapRejectRefundRequest sapOrderRequest) {
        if (sapOrderRequest == null) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常");
        }

        ValueHolderV14 v14 = this.validate(sapOrderRequest);

        if (v14 != null) {
            return v14;
        }

        // IpBStandplatRefund exist = refundOrderMapper.exist(sapOrderRequest.getZsqdh411t(), sapOrderRequest
        // .getVbeln());
        IpBStandplatRefund exist = refundOrderMapper.exist(sapOrderRequest.getZsqdh411t());
        if (exist != null) {
            return ValueHolderV14Utils.getSuccessValueHolder("该退单已存在，不允许重复新增！");
        }

        // 退单的店铺信息要根据退单上的平台单号去找正向订单,以此来获取店铺信息
        CpShop cpShop = this.getShopInfo(sapOrderRequest.getKunag());

        IpBStandplatRefund refund = this.buildStandOrder(sapOrderRequest, cpShop);
        refundOrderMapper.insert(refund);
        List<IpBStandplatRefundItem> refundItems = this.buildStandOrderItems(sapOrderRequest.getItem(),
                sapOrderRequest.getVgbel(), sapOrderRequest.getZsqdh411t(), refund.getId());
        refundOrderItemMapper.batchInsert(refundItems);

        List<MqModel> mqModels = this.buildArr(refund);
        try {
            r3SendMqUtil.sendR3OmsTransferMqInfo(mqModels, ChannelType.STANDPLAT, OperateType.TRANSFER_ORDER,
                    OrderType.REFUND, UUID.randomUUID().toString());
        } catch (Exception e) {
            log.error(LogUtil.format("SAP拒单转单消息发送失败={}",
                    "SapOrderCommonService"), Throwables.getStackTraceAsString(e));
        }
        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "success", mqModels);
    }


    /**
     * 校验退订单信息
     *
     * @param orderRequest orderRequest
     * @return v14
     */
    public ValueHolderV14 validate(SapRejectRefundRequest orderRequest) {
        if (orderRequest == null) {
            return ValueHolderV14Utils.getFailValueHolder("请求内容为空!");
        }
        if (StringUtils.isBlank(orderRequest.getZsqdh411t())) {
            return ValueHolderV14Utils.getFailValueHolder("ZSQDH_411T(411T申请单号)不能为空!");
        }
        if (StringUtils.isBlank(orderRequest.getKunag())) {
            return ValueHolderV14Utils.getFailValueHolder("KUNAG(售达方)不能为空!");
        } else {
            CpShop shopInfo = getShopInfo(orderRequest.getKunag());
            if (shopInfo.getId() == null) {
                return ValueHolderV14Utils.getFailValueHolder(orderRequest.getKunag() + "在中台店铺档案中不存在");
            }
        }
        if (CollectionUtils.isEmpty(orderRequest.getItem())) {
            return ValueHolderV14Utils.getFailValueHolder("item(明细)不能为空!");
        }
        for (SapRejectRefundRequest.Item item : orderRequest.getItem()) {
            if (Objects.isNull(item.getPosnr())) {
                return ValueHolderV14Utils.getFailValueHolder("POSNR(行号)不能为空!");
            }
            if (StringUtils.isBlank(item.getMatnr())) {
                return ValueHolderV14Utils.getFailValueHolder("MATNR(条码编码)不能为空!");
            } else {
                ProductSku productSku = psCoreRpcService.selectProductSku(item.getMatnr());
                if (productSku == null) {
                    return ValueHolderV14Utils.getFailValueHolder(item.getMatnr() + "条码在商品档案中不存在");
                }
            }
            if (item.getRdemng() == null || BigDecimal.ZERO.compareTo(item.getRdemng()) == 0) {
                return ValueHolderV14Utils.getFailValueHolder("RDEMNG(拒收退回数量)不能为空!");
            }
//            else {
//                ValueHolderV14 v14 = rpcOmsFiService.validateCanRefund(orderRequest.getLifex(), item.getMatnr(),
//                        item.getRdemng());
//                if (!v14.isOK()) {
//                    return ValueHolderV14Utils.getFailValueHolder("退货数量大于可退数量，退货失败! 详细信息:" + v14.getMessage());
//                }
//            }
            if (StringUtils.isBlank(item.getLifnr())) {
                return ValueHolderV14Utils.getFailValueHolder("LIFNR(物流承运商)不能为空!");
            }
            if (StringUtils.isBlank(item.getLgort())) {
                return ValueHolderV14Utils.getFailValueHolder("UMLGO(退货仓库)不能为空!");
            } else {
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryOmsWarehouseJStore(item.getLgort());
                if (cpCPhyWarehouse == null || cpCPhyWarehouse.getId() == null) {
                    return ValueHolderV14Utils.getFailValueHolder(item.getLgort() + "仓库在中台实体仓档案中不存在");
                }
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void save(IpBStandplatRefund refund, List<IpBStandplatRefundItem> refundItems) {
        refundOrderMapper.insert(refund);
        refundOrderItemMapper.batchInsert(refundItems);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void save(IpBStandplatOrder order, List<IpBStandplatOrderItem> itemList) {
        standOrderMapper.insert(order);
        standOrderItemMapper.batchInsert(itemList);
    }

    /**
     * 根据店铺编码获取店铺信息
     *
     * @param shopCode 店铺编码
     * @return
     */
    private CpShop getShopInfo(String shopCode) {
        CpShop cpShop = cpRpcExtService.queryByShopCode(shopCode);
        if (cpShop == null) {
            throw new NDSException("根据" + shopCode + "查询R3店铺信息异常!店铺不存在!");
        }
//        if (!PlatFormEnum.SAP.getLongVal().equals(cpShop.getCpCPlatformId())) {
//            throw new NDSException("根据" + shopCode + "获取R3店铺信息异常!店铺平台不为SAP!");
//        }
        return cpShop;
    }

    /**
     * 赋值店铺信息
     *
     * @param order  订单信息
     * @param cpShop 店铺信息
     */
    private void setShopInfo(IpBStandplatOrder order, CpShop cpShop) {
        order.setCpCShopId(cpShop.getId());
        // todo 服务异常临时注释
        order.setCpCShopTitle(cpShop.getCpCShopTitle());
        order.setCpCShopEcode(cpShop.getEcode());
    }

    /**
     * 赋值店铺信息
     *
     * @param order  订单信息
     * @param cpShop 店铺信息
     */
    private void setShopInfo(IpBStandplatRefund order, CpShop cpShop) {
        order.setCpCShopId(cpShop.getId());
        // todo 服务异常临时注释
        order.setCpCShopTitle(cpShop.getCpCShopTitle());
        order.setCpCShopEcode(cpShop.getEcode());
    }

    /**
     * 赋值仓库信息
     *
     * @param order     订单信息
     * @param warehouse 仓库信息
     */
    private void setWarehouseInfo(IpBStandplatRefund order, CpCPhyWarehouse warehouse) {
        if (warehouse == null) {
            return;
        }
        // 后期可以在继续放 加油
        // order.setSendBackStoreId(warehouse.getId());
        order.setSendBackStoreName(warehouse.getCpCWarehouseEname());
        order.setSendBackStoreCode(warehouse.getEcode());
    }

    /**
     * 赋值平台信息
     *
     * @param order 通用订单
     */
    private void defaultPlatformInfo(IpBStandplatOrder order) {
        order.setCpCPlatformId(PlatFormEnum.SAP.getCode().longValue());
        // todo 服务异常临时注释
        order.setCpCPlatformEname(PlatFormEnum.SAP.getName());
        order.setCpCPlatformEcode(PlatFormEnum.SAP.getCode().toString());
    }

    /**
     * 赋值平台信息
     *
     * @param order 通用订单
     */
    private void defaultPlatformInfo(IpBStandplatRefund order) {
        order.setCpCPlatformId(PlatFormEnum.SAP.getCode().longValue());
        // todo 服务异常临时注释
        order.setCpCPlatformEname(PlatFormEnum.SAP.getName());
        order.setCpCPlatformEcode(PlatFormEnum.SAP.getCode().toString());
    }


    public IpBStandplatOrder buildOrder(SapPushB2bOrderRequest orderRequest, CpShop cpShop) {
        IpBStandplatOrder order = new IpBStandplatOrder();
        order.setId(ModelUtil.getSequence(STAND_MASTER_TABLE_NAME));
        order.setTid(orderRequest.getTid());
        order.setPrice(orderRequest.getSumAmount());
        order.setReceiverName(orderRequest.getReceiveName());
        order.setBuyerMessage(orderRequest.getRemark());

        order.setPrice(orderRequest.getSumAmount());
        order.setTotalFee(orderRequest.getSumAmount());
        order.setPayment(orderRequest.getSumAmount());
        order.setInsertdate(new Date());
        order.setBuyerNick(orderRequest.getBname());
        order.setEstimatedDeliveryTime(orderRequest.getExpectedDeliveryTime());


        order.setReceiverProvince(orderRequest.getProvince());
        order.setReceiverCity(orderRequest.getCity());
        String areaName = getAreaName(orderRequest);
        order.setReceiverDistrict(areaName);
        order.setReceiverAddress(orderRequest.getAddress());
        order.setReceiverMobile(orderRequest.getReceiveMobile());
        order.setReceiverPhone(orderRequest.getReceivePhone());
        order.setSalesDepartment(orderRequest.getSalesDepartment());
        order.setSalesGroupCode(orderRequest.getSaleGroupCode());
        try {
            order.setTradeCreateTime(DateUtil.dateTimeSecondsFormatter.parse(orderRequest.getCreated() + " " + orderRequest.getErzet()));
        } catch (ParseException e) {
            order.setTradeCreateTime(new Date());
        }
        // todo
        order.setStatus("WAIT_SELLER_SEND_GOODS");
        order.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());

        // sap 特殊字段
        order.setSalesOrganization(orderRequest.getSaleOrg());
        order.setCostCenter(orderRequest.getKostl());
        order.setOrderType(orderRequest.getOrderType());
        order.setWhetherNeedReceipt("X".equalsIgnoreCase(orderRequest.getPodkz()) ? "Y" : "N");
        order.setTradeSource(cpShop.getCpCPlatformId().toString());
        order.setOrderSourcePlatformEcode(orderRequest.getTid());
        // 销售商品属性
        /*
         * C：残次
         * D：大日期
         * E：替换
         * M：样本
         * S：系列
         */
        order.setReserveVarchar05(orderRequest.getAbrvw());

        this.defaultPlatformInfo(order);
        OperatorUtils.setDefaultVal(order);
        return order;
    }

    public List<IpBStandplatOrderItem> buildOrderItems(List<SapPushB2bOrderRequest.Item> items, String tid,
                                                       Long standId) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        ArrayList<IpBStandplatOrderItem> standPlatOrderItems = Lists.newArrayList();
        items.forEach(item -> {
            IpBStandplatOrderItem order = new IpBStandplatOrderItem();

            order.setId(ModelUtil.getSequence(STAND_CHILD_TABLE_NAME));
            order.setTid(tid);
            order.setSkuId(item.getSku());
            order.setOid(tid + "-" + item.getPosnr());
            order.setIpBStandplatOrderId(standId);
            ProductSku productSku = psCoreRpcService.selectProductSku(item.getSku());
            order.setOuterIid(productSku.getEcode());
            order.setOuterSkuId(item.getSku());
            order.setPayment(item.getPrice());
            order.setTotalFee(item.getPrice());
            order.setTitle(item.getProperties());
            order.setNum(item.getQty().longValue());
            // sap 特殊字段
            // order.setExpiryDateType(item.getValidity());
            order.setExpiryDateRange(item.getValidity());
            order.setFactory(item.getFactory());
            order.setPlanLineCategory(item.getPlanLineCategory());
            order.setLabelingRequirements(item.getLabelingRequirements());
            order.setWarehouse(item.getWarehouse());
            // 问题清单 621 当效期为空时，不去设置效期类型
            if (StringUtils.isNotBlank(item.getValidity())) {
                order.setExpiryDateType(EXPIRY_DATE_TYPE01);
            }

            /**
             * tob部分出库，sap订单增加赠品标识
             * 订单行项目类型字段PSTYV，当PSTYV=ZTN1时，该行是赠品行
             */
            if ("ZTN1".equals(item.getGift())) {
                order.setIsPresent("true");
            }

            this.setDefaultVal(order);
            standPlatOrderItems.add(order);
        });

        return standPlatOrderItems;
    }


    /**
     * build 退单 model
     *
     * @param orderRequest 退单request
     * @param cpShop       店铺信息
     * @return
     */
    public IpBStandplatRefund buildStandOrder(SapPushB2bOrderRequest orderRequest, CpShop cpShop) {
        IpBStandplatRefund refund = new IpBStandplatRefund();
        //        refund.setReturnNo(orderRequest.getRefundNo());
        refund.setReturnNo(orderRequest.getTid());
        refund.setCompanyName(orderRequest.getExpressCompany());
        // refund.setLogisticsNo();
        refund.setBuyerRemark(orderRequest.getRemark());
        try {
            refund.setCreated(DateUtil.dateTimeSecondsFormatter.parse(orderRequest.getCreated() + " " + orderRequest.getErzet()));
        } catch (ParseException e) {
            refund.setCreated(new Date());
        }
        refund.setRefundType(RefundTypeEnum.RETURNS_AND_REFUNDS.getVal());
        refund.setOrderPushType(orderRequest.getOrderPushType());
        refund.setExchangeReceiverName(orderRequest.getReceiveName());
        refund.setExchangeReceiverProvince(orderRequest.getProvince());
        refund.setExchangeReceiverCity(orderRequest.getCity());
        String areaName = getAreaName(orderRequest);

        refund.setExchangeReceiverDistrict(areaName);
        refund.setExchangeReceiverAddress(orderRequest.getAddress());
        refund.setExchangeReceiverMobile(orderRequest.getReceiveMobile());
        refund.setExchangeReceiverPhone(orderRequest.getReceivePhone());
        refund.setReserveVarchar05(PLATFORM_CODE);
        refund.setBuyerNick(orderRequest.getBname());

        refund.setRefundAmount(BigDecimal.ZERO);
        refund.setReturnStatus(OcOrderRefundStatusEnum.WAIT_BUYER_RETURN_GOODS.getVal());

        // sap 特殊字段
        refund.setSalesOrganize(orderRequest.getSaleOrg());
        refund.setCostCenter(orderRequest.getKostl());
        refund.setOrderPushType(orderRequest.getOrderPushType());
        refund.setBusinessTypeCode(orderRequest.getOrderType());
        this.setWarehouseInfo(refund, orderRequest.getCpCPhyWarehouse());

        this.setShopInfo(refund, cpShop);
        this.defaultPlatformInfo(refund);
        this.setDefaultVal(refund);

        refund.setId(ModelUtil.getSequence(STAND_REFUND_MASTER_TABLE_NAME));
        refund.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());
        return refund;
    }

    private String getAreaName(SapPushB2bOrderRequest orderRequest) {
        String areaName = StringUtils.isEmpty(orderRequest.getArea()) ? "其他区" : orderRequest.getArea();
        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(orderRequest.getProvince(),
                orderRequest.getCity(), areaName);
        if (provinceCityAreaInfo.getAreaInfo() == null) {
            areaName = "其它区";
            ProvinceCityAreaInfo provinceCityAreaInfo2 = this.regionService.selectProvinceCityAreaInfo(orderRequest.getProvince(),
                    orderRequest.getCity(), areaName);
            if (provinceCityAreaInfo2.getAreaInfo() == null) {
                areaName = "其他区";
            }
        }
        return areaName;
    }


    /**
     * build 退单 model
     *
     * @param orderRequest 退单request
     * @param cpShop       店铺信息
     * @return
     */
    public IpBStandplatRefund buildStandOrder(SapRejectRefundRequest orderRequest, CpShop cpShop) {
        IpBStandplatRefund refund = this.convert(orderRequest);

        refund.setId(ModelUtil.getSequence(STAND_REFUND_MASTER_TABLE_NAME));
        refund.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());

        this.setShopInfo(refund, cpShop);
        this.defaultPlatformInfo(refund);

        this.setDefaultVal(refund);
        return refund;
    }

    private IpBStandplatRefund convert(SapRejectRefundRequest orderRequest) {
        IpBStandplatRefund refund = new IpBStandplatRefund();

        refund.setReturnNo(orderRequest.getZsqdh411t());
        refund.setOrderNo(orderRequest.getItem().get(0).getOrderNo());
        refund.setBuyerRemark(orderRequest.getKunag());
        refund.setRefundType(RefundTypeEnum.RETURNS_AND_REFUNDS.getVal());
        refund.setCreated(Optional.ofNullable(orderRequest.getBldat411t()).orElse(new Date()));
        // refund.setRefundAmount(orderRequest.get());
        refund.setReturnStatus(OcOrderRefundStatusEnum.WAIT_BUYER_RETURN_GOODS.getVal());
        // 拒单好像没有业务了类型
        // refund.setOrderPushType(orderRequest.getKostl());
        refund.setBusinessTypeCode(orderRequest.getOrderType());
        refund.setReserveVarchar05(PLATFORM_CODE);
        refund.setOriginalBillNo(orderRequest.getLifex());
        refund.setSendBackStoreCode(orderRequest.getItem().get(0).getLgort());
        refund.setDeliveryOrderNo(orderRequest.getVbeln());
        refund.setCostCenter(orderRequest.getKostl());
        refund.setSalesOrganize(orderRequest.getVkorg());
        refund.setCompanyName(orderRequest.getItem().get(0).getLifnr());
        refund.setLogisticsNo(orderRequest.getLogisticsNo());

        return refund;
    }


    /**
     * 转换通用明细
     *
     * @return
     */
    public List<IpBStandplatRefundItem> buildStandOrderItem(List<SapPushB2bOrderRequest.Item> items, String tid,
                                                            String refundNo, Long materId) {

        List<IpBStandplatRefundItem> list = new ArrayList<>();
        items.forEach(item -> {
            list.add(this.buildStandOrderItem(item, tid, refundNo, materId));
        });
        return list;
    }

    /**
     * 转换通用明细
     *
     * @return
     */
    public IpBStandplatRefundItem buildStandOrderItem(SapPushB2bOrderRequest.Item item, String tid, String refundNo,
                                                      Long materId) {

        IpBStandplatRefundItem refundItem = new IpBStandplatRefundItem();

        refundItem.setId(ModelUtil.getSequence(STAND_REFUND_CHILD_TABLE_NAME));
        refundItem.setSubOrderId(String.valueOf(item.getPosnr()));
        refundItem.setSku(item.getSku());
        ProductSku productSku = psCoreRpcService.selectProductSku(item.getSku());
        refundItem.setOuterGoodsId(productSku.getEcode());
        refundItem.setReturnNo(refundNo);
        refundItem.setPrice(item.getPrice().divide(item.getQty(), 4, RoundingMode.HALF_UP));
        refundItem.setQuantity(item.getQty());
        refundItem.setReturnQuantity(item.getQty());
        refundItem.setTitle(item.getProperties());
        refundItem.setIpBStandplatRefundId(materId);
        refundItem.setRefundFee(BigDecimal.ZERO);

        refundItem.setWarehouse(item.getWarehouse());

        OperatorUtils.setDefaultVal(refundItem);

        return refundItem;
    }


    /**
     * 转换通用明细
     *
     * @return
     */
    public List<IpBStandplatRefundItem> buildStandOrderItems(List<SapRejectRefundRequest.Item> items, String tid,
                                                             String refundNo, Long materId) {

        ArrayList<IpBStandplatRefundItem> list = Lists.newArrayList();
        for (SapRejectRefundRequest.Item item : items) {
            list.add(this.buildRejectStandOrderItem(item, tid, refundNo, materId));
        }

        return list;
    }

    /**
     * 转换通用明细
     *
     * @return
     */
    public IpBStandplatRefundItem buildRejectStandOrderItem(SapRejectRefundRequest.Item item, String tid,
                                                            String refundNo, Long materId) {

        IpBStandplatRefundItem refundItem = new IpBStandplatRefundItem();

        refundItem.setId(ModelUtil.getSequence(STAND_REFUND_CHILD_TABLE_NAME));
        refundItem.setSubOrderId(item.getOrderNo() + "-" + item.getPosnr());
        refundItem.setSku(item.getMatnr());
        refundItem.setReturnNo(refundNo);
        refundItem.setReturnQuantity(item.getRdemng());
        // refundItem.setPrice(item.getPrice());
        refundItem.setQuantity(item.getRdemng());
        refundItem.setIpBStandplatRefundId(materId);
        // refundItem.setRefundFee(item.getPrice());
        refundItem.setDeliveryOrderNumber(String.valueOf(item.getPosnrVl()));
        OperatorUtils.setDefaultVal(refundItem);

        return refundItem;
    }

    /**
     * 校验退订单信息
     *
     * @param orderRequest orderRequest
     * @return v14
     */
    public ValueHolderV14 validate(SapPushB2bOrderRequest orderRequest, boolean isRefund) {
        if (orderRequest == null) {
            return ValueHolderV14Utils.getFailValueHolder("请求内容为空!");
        }
        if (StringUtils.isBlank(orderRequest.getTid())) {
            return ValueHolderV14Utils.getFailValueHolder("VBELN(平台单号)不能为空!");
        }

        if (Objects.isNull(orderRequest.getSumAmount())) {
            return ValueHolderV14Utils.getFailValueHolder("KWERT(合计金额)不能为空!");
        }

        if (isRefund && StringUtils.isBlank(orderRequest.getExpressCompany())) {
            return ValueHolderV14Utils.getFailValueHolder("LIFNR(物流承运商)不能为空!");
        }

        if (StringUtils.isBlank(orderRequest.getShopCode())) {
            return ValueHolderV14Utils.getFailValueHolder("KUNAG(店铺信息)不能为空!");
        }

        if (StringUtils.isEmpty(orderRequest.getCreated())) {
            return ValueHolderV14Utils.getFailValueHolder("创建日期不能为空!");
        }

        if (StringUtils.isEmpty(orderRequest.getErzet())) {
            return ValueHolderV14Utils.getFailValueHolder("创建时间不能为空!");
        }

        if (StringUtils.isEmpty(orderRequest.getCreatedName())) {
            return ValueHolderV14Utils.getFailValueHolder("创建人不能为空!");
        }

        if (StringUtils.isEmpty(orderRequest.getOrderType())) {
            return ValueHolderV14Utils.getFailValueHolder("订单类型不能为空!");
        }

        if (StringUtils.isEmpty(orderRequest.getOrderPushType())) {
            return ValueHolderV14Utils.getFailValueHolder("销售推送接口类型不能为空!");
        }

        if (!isRefund) {
            //买家所在省
            String provinceName = orderRequest.getProvince();
            //买家所在市
            String cityName = orderRequest.getCity();
            //买家所在区ID。
            String areaName = getAreaName(orderRequest);
            ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                    cityName, areaName);
            log.debug(LogUtil.format("provinceCityAreaInfo validate={}",
                    "SapOrderCommonService"), provinceCityAreaInfo);
            if (provinceCityAreaInfo == null || provinceCityAreaInfo.getProvinceInfo() == null || provinceCityAreaInfo.getCityInfo() == null || provinceCityAreaInfo.getAreaInfo() == null) {
                return ValueHolderV14Utils.getFailValueHolder("下发的省市区在中台组织档案中不存在");
            }

            if (StringUtils.isEmpty(orderRequest.getAddress())) {
                return ValueHolderV14Utils.getFailValueHolder("详细地址不能为空");
            }

            if (StringUtils.isEmpty(orderRequest.getReceiveName())) {
                return ValueHolderV14Utils.getFailValueHolder("收货人不能为空");
            }

            if (StringUtils.isEmpty(orderRequest.getReceiveMobile())) {
                return ValueHolderV14Utils.getFailValueHolder("联系手机不能为空");
            }

        }
        //        if (isRefund && StringUtils.isBlank(orderRequest.getRefundNo())) {
        //            return ValueHolderV14Utils.getFailValueHolder("ZSQDH(退单编号)不能为空!");
        //        }s
        if (CollectionUtils.isEmpty(orderRequest.getItem())) {
            return ValueHolderV14Utils.getFailValueHolder("item(明细)不能为空!");
        }
        Set<String> extProInfo = null;

        for (SapPushB2bOrderRequest.Item item : orderRequest.getItem()) {
            if (StringUtils.isBlank(item.getSku())) {
                return ValueHolderV14Utils.getFailValueHolder("MATNR(条码编码)不能为空!");
            }
            ProductSku productSku = psCoreRpcService.selectProductSku(item.getSku());
            if (Objects.isNull(productSku)) {
                return ValueHolderV14Utils.getFailValueHolder("MATNR(条码编码)," + item.getSku() + "条码在商品档案中不存在!");
            }

            if (isRefund) {
                if (StringUtils.isBlank(item.getWarehouse())) {
                    return ValueHolderV14Utils.getFailValueHolder("LGORT(库存地点)不能为空!");
                } else {
                    String[] warehouseCodes = StringUtils.split(item.getWarehouse(), ",");
                    for (String warehouseCode : warehouseCodes) {
                        CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryOmsWarehouseJStore(warehouseCode);
                        if (cpCPhyWarehouse == null) {
                            return ValueHolderV14Utils.getFailValueHolder("根据LGORT(库存地点)查询实体仓档案异常!");
                        }
                        orderRequest.setCpCPhyWarehouse(cpCPhyWarehouse);
                    }
                }
            } else {
                if (extProInfo == null) {
                    extProInfo = new HashSet<>();
                }
                // M_DIM2_ID  =  10800
                Map<String, OmsProAttributeInfo> proAttributeMap = productSku.getProAttributeMap();
                OmsProAttributeInfo attributeInfo = proAttributeMap.get("M_DIM2_ID");
                if (attributeInfo != null) {
                    extProInfo.add(attributeInfo.getEcode());
                }
            }
            if (Objects.isNull(item.getPosnr())) {
                return ValueHolderV14Utils.getFailValueHolder("POSNR(行号)不能为空!");
            }
            if (item.getPrice() == null) {
                return ValueHolderV14Utils.getFailValueHolder("DMBTR(金额)不能为空!");
            }
            if (isRefund && item.getQty() == null) {
                return ValueHolderV14Utils.getFailValueHolder("LFIMG(数量)不能为空!");
            }
        }
        // sap 实体奶卡校验
        if (!isRefund && CollectionUtils.isNotEmpty(extProInfo)) {
            if (extProInfo.contains("10800") && extProInfo.size() > 1) {
                return ValueHolderV14Utils.getFailValueHolder("奶卡订单类型错误!");
            }
        }
        return null;
    }

    /**
     * ma body
     *
     * @param standPlatOrder standPlatOrderInfo
     * @return
     */
    private List<MqModel> buildArr(IpBStandplatOrder standPlatOrder) {
        List<MqModel> list = Lists.newArrayList();
        MqModel mqModel = new MqModel();
        mqModel.setOrderId(standPlatOrder.getId());
        mqModel.setOrderNo(standPlatOrder.getTid());
        list.add(mqModel);
        return list;
    }


    /**
     * ma body
     *
     * @param standPlatOrder standPlatOrderInfo
     * @return
     */
    private List<MqModel> buildArr(IpBStandplatRefund standPlatOrder) {
        List<MqModel> list = Lists.newArrayList();
        MqModel mqModel = new MqModel();
        mqModel.setOrderId(standPlatOrder.getId());
        mqModel.setOrderNo(standPlatOrder.getReturnNo());
        list.add(mqModel);
        return list;
    }


    private <T extends BaseModel> void setDefaultVal(T orderInfo) {
        orderInfo.setCreationdate(new Date());
        orderInfo.setModifieddate(new Date());
        orderInfo.setModifiername(SystemUserResource.ROOT_USER_NAME);
        orderInfo.setModifierid(SystemUserResource.ROOT_USER_ID);
        orderInfo.setOwnerid(SystemUserResource.ROOT_USER_ID);
        orderInfo.setOwnername(SystemUserResource.ROOT_USER_NAME);
        orderInfo.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        orderInfo.setAdOrgId(SystemUserResource.AD_ORG_ID);
        orderInfo.setIsactive(IsActiveEnum.Y.getKey());
    }

    private <T extends BaseModel> void setUpdateDefaultVal(T orderInfo) {
        orderInfo.setModifieddate(new Date());
        orderInfo.setModifiername(SystemUserResource.ROOT_USER_NAME);
        orderInfo.setModifierid(SystemUserResource.ROOT_USER_ID);
        orderInfo.setOwnerid(SystemUserResource.ROOT_USER_ID);
        orderInfo.setOwnername(SystemUserResource.ROOT_USER_NAME);
        orderInfo.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        orderInfo.setAdOrgId(SystemUserResource.AD_ORG_ID);
        orderInfo.setIsactive(IsActiveEnum.Y.getKey());
    }


    /**
     * getInstance
     *
     * @return
     */
    public static SapOrderCommonService getInstance() {
        return ApplicationContextHandle.getBean(SapOrderCommonService.class);
    }
}
