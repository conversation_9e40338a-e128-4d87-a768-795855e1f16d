package com.jackrain.nea.ip.service.bn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bytenew.sdk.AbstractApiRequest;
import com.bytenew.sdk.AbstractApiResponse;
import com.bytenew.sdk.ApiClient;
import com.bytenew.sdk.ApiException;
import com.bytenew.sdk.DefaultApiClient;
import com.bytenew.sdk.request.FileUrlUploadRequest;
import com.bytenew.sdk.request.MiniColumnListGetRequest;
import com.bytenew.sdk.request.MiniTaskBatchCreateRequest;
import com.bytenew.sdk.request.MiniTaskCreateRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName BnService
 * @Description 班牛对接service
 * <AUTHOR>
 * @Date 2024/11/12 18:59
 * @Version 1.0
 */
@Service
@Slf4j
public class BnService {

    private static final String MINI_API = "https://open.bytenew.com/gateway/api/";

    public static JSONObject invokeBn(AbstractApiRequest abstractApiRequest) {

        ApiClient apiClient = new DefaultApiClient(MINI_API, "3421199063", "e0dae89e709eb467ae400c2ab0677a63", "2");
        try {
            AbstractApiResponse response = apiClient.execute(abstractApiRequest, "91777cf80b91c14c4ffe096568abb6d6");
            String result = response.getBody();
            JSONObject respJson = JSON.parseObject(result);
            return respJson;
        } catch (ApiException e) {
            log.error("班牛接口【{}】请求异常：{}", "", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public ValueHolderV14 queryColumnList(Long projectId) {
        ValueHolderV14 result = new ValueHolderV14();
        MiniColumnListGetRequest miniColumnListGetRequest = new MiniColumnListGetRequest();
        miniColumnListGetRequest.setProjectId(Math.toIntExact(projectId));
        JSONObject respJson = invokeBn(miniColumnListGetRequest);
        result.setCode(0);
        result.setMessage("success");
        result.setData(respJson);
        return result;
    }

    public ValueHolderV14 pushTask(Map<String, String> content, String projectId, String appId, String userId) {
        ValueHolderV14 result = new ValueHolderV14();
        MiniTaskCreateRequest miniTaskUpdateRequest = new MiniTaskCreateRequest();
        miniTaskUpdateRequest.setContents(content);
        miniTaskUpdateRequest.setAppId(Integer.valueOf(appId));
        miniTaskUpdateRequest.setProjectId(Integer.valueOf(projectId));
        JSONObject respJson = invokeBn(miniTaskUpdateRequest);
        result.setCode(0);
        result.setMessage("success");
        result.setData(respJson);
        return result;
    }

    public ValueHolderV14 batchPushTask(List<Map<String, String>> contents, String projectId, String appId, String userId) {
        ValueHolderV14 result = new ValueHolderV14();
        MiniTaskBatchCreateRequest miniTaskBatchCreateRequest = new MiniTaskBatchCreateRequest();
        List<MiniTaskCreateRequest> datas = new ArrayList<>();
        for (Map<String, String> content : contents) {
            MiniTaskCreateRequest miniTaskUpdateRequest = new MiniTaskCreateRequest();
            miniTaskUpdateRequest.setContents(content);
            miniTaskUpdateRequest.setAppId(Integer.valueOf(appId));
            miniTaskUpdateRequest.setProjectId(Integer.valueOf(projectId));
            datas.add(miniTaskUpdateRequest);
        }
        miniTaskBatchCreateRequest.setData(datas);
        JSONObject respJson = invokeBn(miniTaskBatchCreateRequest);
        result.setCode(0);
        result.setMessage("success");
        result.setData(respJson);
        return result;
    }


    public ValueHolderV14 uploadFile(String url, String name, String type) {
        FileUrlUploadRequest fileUrlUploadRequest = new FileUrlUploadRequest();
        List<Map<String, String>> contents = new ArrayList<>();
        Map<String, String> urlMap = Maps.newHashMap();
        urlMap.put("url", url);
        urlMap.put("signUrl", "1");
        urlMap.put("name", name);
        urlMap.put("type", type);
        contents.add(urlMap);
        fileUrlUploadRequest.setContents(contents);
        JSONObject respJson = invokeBn(fileUrlUploadRequest);
        ValueHolderV14 result = new ValueHolderV14();
        result.setCode(0);
        result.setMessage("success");
        result.setData(respJson);
        return result;
    }
}
