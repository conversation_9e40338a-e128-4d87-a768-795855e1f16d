package com.jackrain.nea.ip.service;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.common.KuaiDi100RequestUtil;
import com.jackrain.nea.ip.model.request.SubscribeTrackParam;
import com.jackrain.nea.ip.model.request.SubscribeTrackParameters;
import com.jackrain.nea.ip.model.result.SubscribeTrackResp;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Augment Agent
 * @since : 2024/11/13
 * description : 订阅物流轨迹服务
 */
@Component
@Slf4j
public class SubscribeLogisticsTrackService {

    @Autowired
    private KuaiDi100RequestUtil kuaiDi100RequestUtil;

    @Value("${r3.ip.kuaidi100.callback.url:}")
    private String callbackUrl;

    /**
     * 订阅物流轨迹
     *
     * @param company 快递公司编码
     * @param number  快递单号
     * @param from    出发地城市
     * @param to      目的地城市
     * @param phone   收件人或寄件人手机号
     * @return 订阅结果
     */
    public ValueHolderV14<SubscribeTrackResp> subscribeLogisticsTrack(String company, String number, String from, String to, String phone) {
        ValueHolderV14<SubscribeTrackResp> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        try {
            if (StringUtils.isEmpty(company)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "物流公司不能为空！");
            }
            if (StringUtils.isEmpty(number)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "物流单号不能为空！");
            }

            // 构建订阅参数
            SubscribeTrackParam subscribeTrackParam = new SubscribeTrackParam();
            subscribeTrackParam.setCompany(company);
            subscribeTrackParam.setNumber(number);
            if (StringUtils.isNotEmpty(from)) {
                subscribeTrackParam.setFrom(from);
            }
            if (StringUtils.isNotEmpty(to)) {
                subscribeTrackParam.setTo(to);
            }

            // 构建附加参数
            SubscribeTrackParameters parameters = new SubscribeTrackParameters();
            parameters.setResultv2("4"); // 开通行政区域解析功能
            if (StringUtils.isNotEmpty(phone)) {
                parameters.setPhone(phone);
            }

            // 调用订阅接口
            SubscribeTrackResp resp = kuaiDi100RequestUtil.subscribeTrack(subscribeTrackParam, parameters, callbackUrl);
            if (!"200".equals(resp.getReturnCode())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(resp.getMessage());
                return v14;
            }

            v14.setData(resp);
            return v14;
        } catch (Exception e) {
            log.error("订阅物流轨迹异常", e);
            return new ValueHolderV14<>(ResultCode.FAIL, "订阅物流轨迹异常：" + e.getMessage());
        }
    }
}
