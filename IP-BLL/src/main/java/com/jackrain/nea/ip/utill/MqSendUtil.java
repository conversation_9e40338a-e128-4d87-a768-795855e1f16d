package com.jackrain.nea.ip.utill;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.ip.enums.MqConstant;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date ：Created in 19:04 2019/3/8
 * description ：发送转单消息工具类
 * @ Modified By：
 */
@Component
@Slf4j
public class MqSendUtil {
//    @Autowired
//    private R3SendMqUtil r3SendMqUtil;

    @Resource
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private PropertiesConf pconf;

    /**
     * 发送转单mq消息
     *
     * @param data 原数据
     */
    public void sendR3OmsTransferMqInfo(JSONArray data, ChannelType channelType, OperateType operateType, OrderType orderType, String originalKey) {
        if (CollectionUtils.isNotEmpty(data)) {
            JSONArray sendArr = new JSONArray();
            for (Object datum : data) {
                JSONObject tempObj = JSONObject.parseObject(datum.toString(), Feature.OrderedField);
                OperateOrderMqInfo operateOrderMqInfo = new OperateOrderMqInfo();
                operateOrderMqInfo.setChannelType(channelType);
                operateOrderMqInfo.setOperateType(operateType);
                operateOrderMqInfo.setOrderType(orderType);
                operateOrderMqInfo.setOrderId(tempObj.getLongValue("orderId"));
                operateOrderMqInfo.setOrderNo(tempObj.getString("orderNo"));
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(operateOrderMqInfo), Feature.OrderedField);
                sendArr.add(jsonObject);
            }
            MqSendResult mqSendResult;
            if (channelType.equals(ChannelType.TAOBAO) && orderType.equals(OrderType.REFUND)) {
                if (log.isDebugEnabled()) {
                    log.debug("淘宝退单转单消息发送！！！");
                }
//                jsonObject = r3SendMqUtil.sendMqinfo(sendArr, pconf.getProperty("r3.oms.call.transfer.taobao.refund.topic"), pconf.getProperty("r3.oms.call.transfer.tag"));
                mqSendResult = defaultProducerSend.sendTopic(MqConstant.r3_oms_call_transfer_topic, MqConstant.r3_oms_call_transfer_tag, JSON.toJSONString(sendArr), "");
            } else {
//                jsonObject = r3SendMqUtil.sendMqinfo(sendArr, pconf.getProperty("r3.oms.call.transfer.topic"), pconf.getProperty("r3.oms.call.transfer.tag"));
                mqSendResult = defaultProducerSend.sendTopic(MqConstant.r3_oms_call_transfer_topic, MqConstant.r3_oms_call_transfer_tag, JSON.toJSONString(sendArr), "");
            }
            log.info("原消息key：" + originalKey + "，" + channelType.name() + "-" + orderType.name() + "转单消息发送：" + JSON.toJSONString(mqSendResult));
        }
    }

    /**
     * 发送PO单mq消息
     *
     * @param jsonObject 原数据
     */
    public void sendR3OmsPoListMqInfo(JSONObject jsonObject) {
        int succcount = jsonObject.getIntValue("succcount");
        JSONObject datares = jsonObject.getJSONObject("data");
        datares.put("success_count", succcount);
//        r3SendMqUtil.sendMqinfo(datares, pconf.getProperty("r3.oms.call.loadpo.topic"), pconf.getProperty("r3.oms.call.loadpo.tag"));
    }
}
