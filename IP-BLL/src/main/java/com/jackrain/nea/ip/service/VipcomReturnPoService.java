package com.jackrain.nea.ip.service;

import com.jackrain.nea.request.VipcomReturnPoSendDrpRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-11-03 22:33
 * @Description : 唯品会退供PO单
 **/
@Slf4j
@Service
public class VipcomReturnPoService {

    @Autowired
    private ThirdPartyLogService thirdPartyLogService;


    /**
     * 唯品会退供PO单 -> DRP
     *
     * @param request
     * @return
     */
    public ValueHolderV14 sendDRP(VipcomReturnPoSendDrpRequest request) {
        ValueHolderV14 valueHolderV14 = ValueHolderV14Utils.getSuccessValueHolder("推送DRP成功！");
//        log.info("VipcomReturnPoService.request：{}", JSON.toJSONString(request));
//        try{
//            JSONObject requestJson = JSON.parseObject(JSON.toJSONString(request));
//            JSONObject result = drpRestHttpClient.post(BillTypeAndMethodEnum.VIP_RETURN_PO_ORDER_01.getMethod(),requestJson);
//            int code = Integer.valueOf(String.valueOf(result.get("code")));
//            if(ResultCode.SUCCESS != code){
//                code = ResultCode.FAIL;
//            }
//            String message = String.valueOf(result.get("message"));
//            try{
//                Date requestTime = new Date();
//                //记录调用日志
//                IpBThirdPartyInterfaceLog thirdPartyInterfaceLog = thirdPartyLogService.buildThirdPartyLog(requestTime, JSON.toJSONString(request), JSON.toJSONString(result), JSON.toJSONString(result), code, request.getBSoDocNo(), BillTypeAndMethodEnum.VIP_RETURN_PO_ORDER_01);
//                thirdPartyLogService.insertLogData(thirdPartyInterfaceLog);
//            }catch (Exception e){
//                log.error("创建DRP接口日志信息异常:{}", Throwables.getStackTraceAsString(e));
//            }
//            valueHolderV14.setCode(code);
//            valueHolderV14.setMessage(message);
//        }catch (Exception e){
//            log.error("唯品会退供PO单推送DRP失败 errorMsg",e);
//            valueHolderV14.setCode(ResultCode.FAIL);
//            valueHolderV14.setMessage("唯品会退供PO单推送DRP失败");
//        }
        return valueHolderV14;
    }

}
