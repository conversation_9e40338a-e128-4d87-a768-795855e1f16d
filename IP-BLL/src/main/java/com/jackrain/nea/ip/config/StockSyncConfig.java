package com.jackrain.nea.ip.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * description：斯凯奇wing配置
 *
 * <AUTHOR>
 * @date 2021/7/17
 */
@Data
@Configuration
public class StockSyncConfig {
    public static final String IDENTITY_KEY = "Identity-Key";
    public static final String IDENTITY_VALUE = "r3-sg";
    public static final String REQUEST_TIME = "Request-Time";
    public static final String Cloudhub_Access = "Cloudhub-Access";

    @Value("${skx.stock.sync.url:http://cloudhub-gateway-test.cloudhub.oneretail.cn/cloudhub/storage/sync}")
    private String apiUrl;

}
