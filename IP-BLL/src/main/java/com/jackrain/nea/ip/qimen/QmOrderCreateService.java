package com.jackrain.nea.ip.qimen;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.clientdao.contracts.StandplatOrderDaoCmd;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpCPlatformQueryCmd;
import com.jackrain.nea.cpext.api.CshopQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.result.CpCRegionAliasResult;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.common.QiMenConstant;
import com.jackrain.nea.ip.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.ip.model.ThirdPlatformLog;
import com.jackrain.nea.ip.utill.QiMenUtil;
import com.jackrain.nea.psext.api.PsCSkuThirdItemQueryCmd;
import com.jackrain.nea.psext.result.PsCSkuThirdItemResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.qimen.api.QimenResponse;
import com.qimen.api.request.DeliveryorderCreateRequest;
import com.qimen.api.response.DeliveryorderCreateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/1
 */

@Slf4j
@Component(QiMenConstant.PREFIX + QiMenConstant.QIMEN_DELIVERYORDER_CREATE_DEFAULT)
public class QmOrderCreateService implements CallBackApi {

    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCSkuThirdItemQueryCmd psSkuThirdItemQueryCmd;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CshopQueryCmd cshopQueryCmd;

    @DubboReference(group = "cp-ext", version = "1.0")
    private RegionQueryExtCmd regionQueryExtCmd;

    @DubboReference(group = "cp-ext", version = "1.0")
    private CpCPlatformQueryCmd cpCPlatformQueryCmd;

    @Autowired
    private StandplatOrderDaoCmd standplatOrderDaoCmd;

    @Autowired
    private IpBStandplatOrderMapper standplatOrderMapper;

    private static final String OWNERCODE200315 = "RYYTN200315";
    private static final int NICK_TOO_LONG = 200;
    private static final int SELLER_MESSAGE_TOO_LONG = 500;


    @Override
    public QimenResponse execute(String requestBody, ThirdPlatformLog thirdPlatformLog) {
        DeliveryorderCreateResponse orderCreateResponse = new DeliveryorderCreateResponse();

        log.info(LogUtil.format("插入通用订单接口:{}", "QmOrderCreateService"), requestBody);

        try {
            DeliveryorderCreateRequest orderCreateRequest =
                    (DeliveryorderCreateRequest) QiMenUtil.parseXml(requestBody,
                            DeliveryorderCreateRequest.class);
            if (Objects.nonNull(orderCreateRequest) && Objects.nonNull(orderCreateRequest.getDeliveryOrder())) {
                thirdPlatformLog.setBillNo(orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode());
            }
            ValueHolderV14 checkV14 = checkParamAndSave(orderCreateRequest);
            if (!checkV14.isOK()) {
                orderCreateResponse.setCode("-1");
                orderCreateResponse.setFlag("failure");
                orderCreateResponse.setMessage(checkV14.getMessage());
                return orderCreateResponse;
            } else {
                orderCreateResponse.setCode("0");
                orderCreateResponse.setFlag("success");
                orderCreateResponse.setMessage("创建成功");
                orderCreateResponse.setDeliveryOrderId(String.valueOf(checkV14.getData()));
            }

        } catch (Exception e) {
            log.error(LogUtil.format("QmOrderCreateService.execute error:{}", "QmOrderCreateService.execute"),
                    Throwables.getStackTraceAsString(e));
            return QiMenUtil.setResponse(String.valueOf(ResultCode.FAIL), QiMenConstant.FAILURE,
                    "创建失败" + e.getMessage());
        }

        return orderCreateResponse;
    }

    private ValueHolderV14 checkParamAndSave(DeliveryorderCreateRequest orderCreateRequest) throws Exception {
        if (Objects.isNull(orderCreateRequest) || Objects.isNull(orderCreateRequest.getDeliveryOrder())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "发货单信息不能为空!");
        }

        if (orderCreateRequest.getOrderLines().stream().anyMatch(i -> StringUtils.isBlank(i.getOwnerCode()))) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细货主编码不能为空!");
        }

        String ownerCode = orderCreateRequest.getOrderLines().get(0).getOwnerCode();
        ValueHolderV14<CpShop> cpShopValueHolderV14 = cshopQueryCmd.queryByOwnerCode(ownerCode);
        if (!cpShopValueHolderV14.isOK()) {
            return new ValueHolderV14<>(ResultCode.FAIL, cpShopValueHolderV14.getMessage());
        }
        CpShop shop = cpShopValueHolderV14.getData();

        log.info(LogUtil.format("平台:{}", "checkParamAndSave"), shop.getCpCPlatformId());

        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "出库单号不能为空!");
        } else {
            //判断tid 是否存在，存在就直接返回成功
            String tid = orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode();
            //旺店通平台推送的订单在生成【通用订单中间表】时，平台单号字段取值：用接口参数ownerCode（货主编码）拼接deliveryOrderCode（出库单号），格式为：deliveryOrderCode-ownerCode
            if (QiMenConstant.WANG_DIAN_TONG_ID.equals(shop.getCpCPlatformId())) {
                List<DeliveryorderCreateRequest.OrderLine> orderLines = orderCreateRequest.getOrderLines();
                if (CollectionUtils.isNotEmpty(orderLines)) {
                    tid = orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode() + QiMenConstant.SPLIT + orderLines.get(0).getOwnerCode();
                } else {
                    tid = orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode() + QiMenConstant.SPLIT + orderCreateRequest.getDeliveryOrder().getOwnerCode();
                }
            }
            if (standplatOrderMapper.existTid(tid) > 0) {
                return new ValueHolderV14<>(ResultCode.SUCCESS, "出库单号已存在保存完成!");
            }
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getSourcePlatformCode())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "订单来源平台编码不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getShopNick())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "店铺名称不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getPlaceOrderTime())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "店铺订单的创建时间/下单时间不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getOperateTime())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "操作（审核）时间不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getWarehouseCode())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "仓库编码不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getOperateTime())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "操作（审核）时间不能为空!");
        }
        if (Objects.isNull(orderCreateRequest.getDeliveryOrder().getReceiverInfo())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "收件人信息不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getReceiverInfo().getName())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "姓名不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getReceiverInfo().getProvince())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "省份不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getReceiverInfo().getCity())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "城市不能为空!");
        }
        if (StringUtils.isBlank(orderCreateRequest.getDeliveryOrder().getReceiverInfo().getMobile())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "移动电话不能为空!");
        }
        if (CollectionUtils.isEmpty(orderCreateRequest.getOrderLines())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细信息不能为空!");
        }
        if (orderCreateRequest.getOrderLines().stream().anyMatch(i -> StringUtils.isBlank(i.getItemCode()))) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细商品编码不能为空!");
        }
        if (orderCreateRequest.getOrderLines().stream().anyMatch(i -> StringUtils.isBlank(i.getPlanQty()))) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细应发商品数量不能为空!");
        }
        if (orderCreateRequest.getOrderLines().stream().anyMatch(i -> StringUtils.isBlank(i.getActualPrice()))) {
            return new ValueHolderV14<>(ResultCode.FAIL, "明细实际成交价不能为空!");
        }

        //昵称超长处理
        String buyerNick = orderCreateRequest.getDeliveryOrder().getBuyerNick();
        if (StringUtils.isNotBlank(buyerNick) && buyerNick.length() > NICK_TOO_LONG) {
            orderCreateRequest.getDeliveryOrder().setBuyerNick(buyerNick.substring(0, NICK_TOO_LONG));
        }

        // 超长处理
        String sellerMessage = orderCreateRequest.getDeliveryOrder().getSellerMessage();
        if (StringUtils.isNotBlank(sellerMessage) && sellerMessage.length() > SELLER_MESSAGE_TOO_LONG) {
            orderCreateRequest.getDeliveryOrder().setSellerMessage(sellerMessage.substring(0, SELLER_MESSAGE_TOO_LONG));
        }


        /*String shopNick = orderCreateRequest.getDeliveryOrder().getShopNick();
        ValueHolderV14<CpShop> cpShopValueHolderV14 = cshopQueryCmd.queryBySellerNick(shopNick);
        if (!cpShopValueHolderV14.isOK()) {
            return new ValueHolderV14<>(ResultCode.FAIL, cpShopValueHolderV14.getMessage());
        }
        CpShop shop = cpShopValueHolderV14.getData();*/

      /*  ValueHolderV14<CpCPlatform> cpCPlatformValueHolderV14 = cpCPlatformQueryCmd.queryCpCPlatformBySystemCode(orderCreateRequest.getDeliveryOrder().getSourcePlatformCode());
        if (!cpCPlatformValueHolderV14.isOK()) {
            return new ValueHolderV14<>(ResultCode.FAIL, cpCPlatformValueHolderV14.getMessage());
        }
        CpCPlatform platform = cpCPlatformValueHolderV14.getData();*/

        //省市区校验
        String provinceName = orderCreateRequest.getDeliveryOrder().getReceiverInfo().getProvince();
        String cityName = orderCreateRequest.getDeliveryOrder().getReceiverInfo().getCity();
        Map<String, CpCRegionAliasResult> cRegionAliasResultMap =
                regionQueryExtCmd.selectProvinceCityAreaInfoMap(provinceName, cityName, null);
        CpCRegionAliasResult province = cRegionAliasResultMap.get(provinceName);
        CpCRegionAliasResult city = cRegionAliasResultMap.get(cityName);
        if (Objects.isNull(province) || Objects.isNull(city)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "省市区匹配失败!");
        }
        //第三方条码校验
        List<String> itemCodes = orderCreateRequest.getOrderLines().stream()
                .map(DeliveryorderCreateRequest.OrderLine::getItemCode).distinct().collect(Collectors.toList());
        ValueHolderV14<List<PsCSkuThirdItemResult>> thirdItems =
                psSkuThirdItemQueryCmd.queryThirdItemsInfoByDucodes(itemCodes);
        if (!thirdItems.isOK()) {
            return new ValueHolderV14<>(ResultCode.FAIL, thirdItems.getMessage());
        }
        List<PsCSkuThirdItemResult> thirdItemResults = thirdItems.getData();
        Map<String, PsCSkuThirdItemResult> thirdItemResultMap =
                thirdItemResults.stream().collect(Collectors.toMap(PsCSkuThirdItemResult::getDucode,
                        Function.identity()));
        Collection subtract = CollectionUtils.subtract(itemCodes, thirdItemResultMap.keySet());
        if (CollectionUtils.isNotEmpty(subtract)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "第三方条码映射失败,差异条码" + subtract);
        }
        Object mainId = saveMain(orderCreateRequest, shop, provinceName, cityName);
        saveItems(orderCreateRequest, mainId, thirdItemResultMap, shop);
        return new ValueHolderV14<>(mainId, ResultCode.SUCCESS, "参数校验和保存完成!");
    }

    private void saveItems(DeliveryorderCreateRequest orderCreateRequest, Object mainId, Map<String,
            PsCSkuThirdItemResult> thirdItemResultMap, CpShop shop) throws Exception {
        for (DeliveryorderCreateRequest.OrderLine orderLine : orderCreateRequest.getOrderLines()) {
            JSONObject itemJsonObj = new JSONObject();

            itemJsonObj.put("tid", orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode());
            //旺店通平台推送的订单在生成【通用订单中间表】时，平台单号字段取值：用接口参数ownerCode（货主编码）拼接deliveryOrderCode（出库单号），格式为：deliveryOrderCode-ownerCode
            if (QiMenConstant.WANG_DIAN_TONG_ID.equals(shop.getCpCPlatformId())) {
                itemJsonObj.put("tid", orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode() + QiMenConstant.SPLIT + orderLine.getOwnerCode());
            }
            itemJsonObj.put("oid", orderLine.getSourceOrderCode());
            String itemCode = orderLine.getItemCode();
            PsCSkuThirdItemResult thirdItemResult = thirdItemResultMap.get(itemCode);
            itemJsonObj.put("sku_id", itemCode);
            itemJsonObj.put("outer_sku_id", thirdItemResult.getPsCSkuEcode());
            itemJsonObj.put("outer_iid", thirdItemResult.getPsCSkuEcode());
            itemJsonObj.put("title", orderLine.getItemName());
            itemJsonObj.put("num", orderLine.getPlanQty());
            //【认养一头牛项目】针对旺店通海拍客店铺的订单进入到通用订单中间表商品明细中实付金额赋值到商品单价中 (货主编码和店铺一对一)
            if (OWNERCODE200315.equals(orderCreateRequest.getDeliveryOrder().getOwnerCode())) {
                itemJsonObj.put("price", orderLine.getActualPrice());
            } else {
                itemJsonObj.put("price", orderLine.getRetailPrice());
            }
            itemJsonObj.put("total_fee", orderLine.getActualPrice());
            itemJsonObj.put("payment", orderLine.getActualPrice());
            itemJsonObj.put("status", "WAIT_SELLER_SEND_GOODS");
            standplatOrderDaoCmd.saveOrderProductDetail(itemJsonObj, mainId);
        }
    }

    private Object saveMain(DeliveryorderCreateRequest orderCreateRequest, CpShop shop, String provinceName,
                            String cityName) throws Exception {
        JSONObject mainJsonObj = new JSONObject();
        mainJsonObj.put("tid", orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode());
        //旺店通平台推送的订单在生成【通用订单中间表】时，平台单号字段取值：用接口参数ownerCode（货主编码）拼接deliveryOrderCode（出库单号），格式为：deliveryOrderCode-ownerCode
        if (QiMenConstant.WANG_DIAN_TONG_ID.equals(shop.getCpCPlatformId())) {
            List<DeliveryorderCreateRequest.OrderLine> orderLines = orderCreateRequest.getOrderLines();
            if (CollectionUtils.isNotEmpty(orderLines)) {
                mainJsonObj.put("tid", orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode() + QiMenConstant.SPLIT +
                        orderLines.get(0).getOwnerCode());
            } else {
                mainJsonObj.put("tid", orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode() + QiMenConstant.SPLIT +
                        orderCreateRequest.getDeliveryOrder().getOwnerCode());
            }

            /*如果来源平台是【奇门OMS】且交易外部来源等于【OTHERS】则改为【OTHER】*/
            if ("OTHERS".equals(orderCreateRequest.getDeliveryOrder().getSourcePlatformCode())) {
                orderCreateRequest.getDeliveryOrder().setSourcePlatformCode("OTHER");
            }
        }

        mainJsonObj.put("post_fee", orderCreateRequest.getDeliveryOrder().getFreight());
        mainJsonObj.put("platform", shop.getCpCPlatformId());
        mainJsonObj.put("cp_c_platform_ecode", shop.getCpCPlatformEcode());
        mainJsonObj.put("cp_c_platform_ename", shop.getCpCPlatformEname());
        mainJsonObj.put("cp_c_shop_id", shop.getId());
        mainJsonObj.put("cp_c_shop_ecode", shop.getEcode());
        mainJsonObj.put("cp_c_shop_title", shop.getCpCShopTitle());
        mainJsonObj.put("receiver_province", provinceName);
        mainJsonObj.put("receiver_name", orderCreateRequest.getDeliveryOrder().getReceiverInfo().getName());
        mainJsonObj.put("receiver_city", cityName);
        mainJsonObj.put("receiver_district", orderCreateRequest.getDeliveryOrder().getReceiverInfo().getArea());
        mainJsonObj.put("receiver_address", orderCreateRequest.getDeliveryOrder().getReceiverInfo().getDetailAddress());
        mainJsonObj.put("receiver_phone", orderCreateRequest.getDeliveryOrder().getReceiverInfo().getTel());
        mainJsonObj.put("receiver_mobile", orderCreateRequest.getDeliveryOrder().getReceiverInfo().getMobile());
        mainJsonObj.put("seller_memo", orderCreateRequest.getDeliveryOrder().getSellerMessage());
        mainJsonObj.put("seller_nick", orderCreateRequest.getDeliveryOrder().getSellerNick());
        mainJsonObj.put("buyer_nick", orderCreateRequest.getDeliveryOrder().getBuyerNick());
        mainJsonObj.put("buyer_message", orderCreateRequest.getDeliveryOrder().getSellerMessage());
        mainJsonObj.put("trade_create_time", orderCreateRequest.getDeliveryOrder().getPlaceOrderTime());
        mainJsonObj.put("insertdate", new Date());
        mainJsonObj.put("status", "WAIT_SELLER_SEND_GOODS");
        mainJsonObj.put("total_fee", orderCreateRequest.getDeliveryOrder().getTotalAmount());
        mainJsonObj.put("price", orderCreateRequest.getDeliveryOrder().getItemAmount());
        mainJsonObj.put("payment", orderCreateRequest.getDeliveryOrder().getGotAmount());
        mainJsonObj.put("ownerename", orderCreateRequest.getDeliveryOrder().getOperatorName());
        mainJsonObj.put("ownername", orderCreateRequest.getDeliveryOrder().getOperatorName());
        mainJsonObj.put("creationdate", orderCreateRequest.getDeliveryOrder().getOperateTime());
        mainJsonObj.put("order_source_platform_ecode", orderCreateRequest.getOrderLines().get(0).getSourceOrderCode());
        mainJsonObj.put("source_bill_no", orderCreateRequest.getDeliveryOrder().getDeliveryOrderCode());
        mainJsonObj.put("trade_source", orderCreateRequest.getDeliveryOrder().getSourcePlatformCode());
        mainJsonObj.put("oaid", orderCreateRequest.getDeliveryOrder().getReceiverInfo().getOaid());

        mainJsonObj.put("extendProps", orderCreateRequest.getExtendProps());

        return standplatOrderDaoCmd.saveOrder(mainJsonObj);
    }

    @Override
    public void buildThirdPlatformLog(ThirdPlatformLog log) {
        log.setThirdPlatform("旺店通");
        log.setMethod(QiMenConstant.QIMEN_DELIVERYORDER_CREATE);
        log.setMethodCn("发货单创建接口");
        log.setBillType(101L);
        log.setForm("奇门");
        log.setTo("中台");
        log.setInterfaceType(1);
    }
}
