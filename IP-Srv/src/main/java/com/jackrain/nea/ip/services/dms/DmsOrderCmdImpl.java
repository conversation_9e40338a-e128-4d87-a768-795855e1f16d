package com.jackrain.nea.ip.services.dms;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.api.dms.DmsOrderCmd;
import com.jackrain.nea.ip.manager.IpBThirdPartyInterfaceLogManager;
import com.jackrain.nea.ip.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.ip.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.ip.model.dms.request.DmsOrderAddRequest;
import com.jackrain.nea.ip.model.dms.request.DmsRefundAddRequest;
import com.jackrain.nea.ip.rpc.CpRpcExtService;
import com.jackrain.nea.ip.service.dms.DmsLabelingRequirementsQueryService;
import com.jackrain.nea.ip.service.dms.DmsOrderCancelService;
import com.jackrain.nea.ip.service.dms.DmsOrderQueryService;
import com.jackrain.nea.ip.service.dms.DmsOrderSaveService;
import com.jackrain.nea.ip.service.dms.DmsRefundSaveService;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName DmsOrderCmdImpl
 * @Description dms
 * <AUTHOR>
 * @Date 2024/6/1 17:11
 * @Version 1.0
 */
@Component
@Slf4j
public class DmsOrderCmdImpl implements DmsOrderCmd {

    private static final Integer FUN_ORDER = 1;
    private static final Integer FUN_RETURN = 2;

    @Autowired
    private DmsOrderSaveService dmsOrderSaveService;
    @Autowired
    private IpBStandplatOrderMapper standplatOrderMapper;
    @Autowired
    private CpRpcExtService cpRpcExtService;
    @Autowired
    private DmsRefundSaveService dmsRefundSaveService;
    @Autowired
    private IpBStandplatRefundMapper standplatRefundMapper;
    @Autowired
    private IpBThirdPartyInterfaceLogManager thirdPartyInterfaceLogManager;
    @Autowired
    private DmsLabelingRequirementsQueryService labelingRequirementsQueryService;
    @Autowired
    private DmsOrderCancelService dmsOrderCancelService;
    @Autowired
    private DmsOrderQueryService dmsOrderQueryService;

    @Override
    public ValueHolderV14 saveStandPlayOrder(DmsOrderAddRequest dmsOrderAddRequest) {
        // 日志打印
        log.info("saveStandPlayOrder dmsOrderAddRequest:{}", JSONUtil.toJsonStr(dmsOrderAddRequest));
        // 增加一些校验
        if (CollectionUtils.isEmpty(dmsOrderAddRequest.getItem())) {
            return ValueHolderV14Utils.getFailValueHolder("订单商品不能为空");
        }
        CpShop cpShop = getShopInfo(dmsOrderAddRequest.getShopCode());
        if (cpShop == null) {
            return ValueHolderV14Utils.getFailValueHolder("订单:" + dmsOrderAddRequest.getTid() + "店铺不存在");
        }
        // 判断订单存不存在
        IpBStandplatOrder exist = standplatOrderMapper.exist(dmsOrderAddRequest.getTid(), cpShop.getId());
        if (exist != null) {
            return ValueHolderV14Utils.getSuccessValueHolder("该订单已存在，不允许重复新增!");
        }
        return dmsOrderSaveService.saveStandPlayOrder(dmsOrderAddRequest, cpShop);
    }

    @Override
    public ValueHolderV14 cancelOrder(JSONObject param) {
        log.info("cancelOrder param:{}", param.toJSONString());
        ValueHolderV14 valueHolderV14 = dmsOrderCancelService.cancelOrder(param);
        Integer fun = param.getInteger("FUN");
        Long billType = 0L;
        if (FUN_ORDER.equals(fun)) {
            billType = 101L;
        }
        if (FUN_RETURN.equals(fun)) {
            billType = 201L;
        }
        thirdPartyInterfaceLogManager.insertThirdInterfaceLog(param.getString("VERLN"), billType, "dmsOrderCancel",
                "中台", "DMS", valueHolderV14.getCode(), new Date(), param.toJSONString(), JSON.toJSONString(valueHolderV14), "DMS");
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14 queryOrder(JSONObject param) {
        log.info("queryOrder param:{}", param.toJSONString());
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        try {
            valueHolderV14 = dmsOrderQueryService.queryOrder(param);
            return valueHolderV14;
        } catch (Exception e) {
            log.error("queryOrder error:{}", e.getMessage(), e);
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14 saveRefundOrder(DmsRefundAddRequest dmsRefundAddRequest) {
        log.info("saveRefundOrder param:{}", JSONUtil.toJsonStr(dmsRefundAddRequest));
        CpShop cpShop = getShopInfo(dmsRefundAddRequest.getShopCode());
        if (cpShop == null) {
            return ValueHolderV14Utils.getFailValueHolder("店铺不存在");
        }
//        String warehouseCode = dmsRefundAddRequest.getWarehouse();
        String warehouseCode = dmsRefundAddRequest.getItem().get(0).getWarehouse();
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryOmsWarehouseJStore(warehouseCode);
        if (cpCPhyWarehouse == null) {
            return ValueHolderV14Utils.getFailValueHolder("仓库不存在");
        }
        IpBStandplatRefund exist = standplatRefundMapper.exist(dmsRefundAddRequest.getTid());
        if (exist != null) {
            return ValueHolderV14Utils.getSuccessValueHolder("该退单已存在，不允许重复新增!");
        }
        return dmsRefundSaveService.saveRefundOrder(dmsRefundAddRequest, cpShop, cpCPhyWarehouse);
    }

    @Override
    public ValueHolderV14 queryLabelingRequirements() {
        log.info("queryLabelingRequirements");
        return labelingRequirementsQueryService.queryLabelingRequirements();
    }

    private CpShop getShopInfo(String shopCode) {
        return cpRpcExtService.queryByShopCode(shopCode);
    }
}
