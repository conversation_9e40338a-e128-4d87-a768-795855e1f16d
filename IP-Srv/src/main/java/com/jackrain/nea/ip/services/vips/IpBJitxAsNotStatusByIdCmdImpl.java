package com.jackrain.nea.ip.services.vips;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.util.TypeUtils;
import com.jackrain.nea.clientdao.utils.TableUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.vips.IpBJitxAsNotSynStatusByIdCmd;
import com.jackrain.nea.ip.mapper.IpBJitxAsStatusByIdMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import java.util.Date;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：Created in 16:04 2019/9/4 description ：
 * @ Modified By：
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.4.0", group = "ip")
public class IpBJitxAsNotStatusByIdCmdImpl extends CommandAdapter implements IpBJitxAsNotSynStatusByIdCmd {
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        log.info("======寻仓订单标记为未同步服务=========");
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        IpBJitxAsStatusByIdMapper mapper = ApplicationContextHandle.getBean(IpBJitxAsStatusByIdMapper.class);

        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        log.info("param======>" + param.toString());

        boolean isIDS = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");

        if (!(isIDS || isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && isObjId) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择需要标记的单据记录！", querySession.getLocale()));
        }

        String tableName = param.getString("table");

        if (StringUtils.isEmpty(tableName)) {
            throw new NDSException(Resources.getMessage("表名为空", querySession.getLocale()));
        }
        tableName = tableName.toLowerCase();
        if (isIDS) {
            //列表批量处理
            vh = batchMarked(param, mapper, tableName, querySession);
        } else {
            //单对象界面处理
            Long objid = param.getLongValue("objid");
            JSONObject jsonObject;
            try {
                jsonObject = separateMarked(objid, mapper, tableName, querySession);
                vh.put("code", TypeUtils.castToInt(jsonObject.get("code")));
                vh.put("message", Resources.getMessage(jsonObject.getString("message"), querySession.getLocale()));
            } catch (Exception e) {
                e.printStackTrace();
                vh.put("code", -1);
                vh.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
            }
        }
        return vh;
    }

    private JSONObject separateMarked(Long objid, IpBJitxAsStatusByIdMapper mapper, String tableName, QuerySession querySession) throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONObject whereKeys = new JSONObject();
        User user = querySession.getUser();
        String uniqueKey = (String) TableUtil.getGetMethod(new TableUtil(), tableName);
        if (StringUtils.isEmpty(uniqueKey)) {
            jsonObject.put("message", Resources.getMessage("标记为未同步失败", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        uniqueKey = uniqueKey.toUpperCase();
        log.info("tableName====>" + tableName + "，uniqueKey======>" + uniqueKey);
        whereKeys.put("ID", objid);
        String[] returnField = {uniqueKey};
        JSONObject search = ElasticSearchUtil.search(tableName, tableName, whereKeys, null, null, 10, 0, returnField);
        JSONArray searchJSONArray = search.getJSONArray("data");
        log.info("es data==========>" + searchJSONArray);
        if (searchJSONArray == null || searchJSONArray.isEmpty()) {
            jsonObject.put("message", Resources.getMessage("标记为未同步失败", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        JSONObject object = searchJSONArray.getJSONObject(0);
        Object uniqueValue = object.get(uniqueKey);
        if (uniqueValue == null) {
            jsonObject.put("message", Resources.getMessage("标记为未同步失败", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        log.info("uniqueValue=========>" + uniqueValue.toString());
        HashMap<String, Object> map = mapper.selectSynStatusByUniqueKey(tableName, uniqueKey, uniqueValue);
        if (MapUtils.isEmpty(map)) {
            jsonObject.put("message", Resources.getMessage("标记为未同步失败", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        int synStatus = Tools.getInt(map.get("SYNSTATUS"), 0);
        String sysRemark = (map.get("sysremark") == null ? "" : (String) map.get("sysremark")) + "手工修改为未同步;";
        if (synStatus == 0) {
            jsonObject.put("message", Resources.getMessage("状态已是未同步不允许单据标记！", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        Date now = new Date(System.currentTimeMillis());
        Integer modifierId = user.getId();
        String ename = user.getEname();
        String name = user.getName();
        Long count = mapper.updateNotSynStatusByUniqueKey(tableName, uniqueKey, uniqueValue, now, sysRemark, modifierId, ename, name);
        log.info("count=======>" + count);
        if (count <= 0) {
            jsonObject.put("message", Resources.getMessage("标记为未同步失败", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }

        JSONObject esObj = new JSONObject();
        esObj.put("SYNSTATUS", 0);
        esObj.put("modifieddate", now);
        esObj.put("update_time", now);
        esObj.put("sysremark", sysRemark);
        esObj.put("modifierid", modifierId);
        esObj.put("modifierename", ename);
        esObj.put("modifiername", name);
        Object o = esObj.toJavaObject(TableUtil.getClassByName(tableName + "_es_model_class").get(0));
        ElasticSearchUtil.updateDocument(tableName, tableName, o, objid);
        jsonObject.put("message", Resources.getMessage("标记为未同步成功", querySession.getLocale()));
        jsonObject.put("code", 0);
        return jsonObject;
    }

    private ValueHolder batchMarked(JSONObject param, IpBJitxAsStatusByIdMapper mapper, String tableName, QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        int success = 0;
        int fail = 0;
        Object[] ids = param.getJSONArray("ids").toArray();
        JSONArray listArray = new JSONArray();
        for (Object id : ids) {
            Long objid = TypeUtils.castToLong(id);
            JSONObject retJson;
            try {
                retJson = separateMarked(objid, mapper, tableName, querySession);
            } catch (Exception e) {
                e.printStackTrace();
                retJson = new JSONObject();
                retJson.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
                retJson.put("code", -1);
                retJson.put("objid", objid);
                listArray.add(retJson);
                fail++;
                continue;
            }
            if (retJson == null) {
                fail++;
                continue;
            }
            if (retJson.containsKey("code") && retJson.getInteger("code") == 0) {
                success++;
            } else {
                fail++;
                listArray.add(retJson);
            }
        }

//        if (ids.length == 1 && listArray.size() == 1){
//            listArray.getJSONObject(0).remove("objid");
//        }

        valueHolder.put("data", listArray);
        if (0 == fail) {
            valueHolder.put("code", 0);
            valueHolder.put("message", Resources.getMessage("标记成功的记录数：" + success + ",失败的记录数：" + fail, querySession.getLocale()));
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", Resources.getMessage("标记成功的记录数：" + success + ",失败的记录数：" + fail, querySession.getLocale()));
        }
        return valueHolder;
    }


}
