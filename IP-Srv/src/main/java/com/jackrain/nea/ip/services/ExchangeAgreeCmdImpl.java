package com.jackrain.nea.ip.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.util.TypeUtils;
import com.jackrain.nea.apiservice.api.request.ExchangeAgreeRequest;
import com.jackrain.nea.apiservice.service.ExchangeAgreeApiService;
import com.jackrain.nea.clientdao.utils.TableUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.api.GeneralOrganizationCmd;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.ExchangeAgreeCmd;
import com.jackrain.nea.ip.mapper.CommonMapper;
import com.jackrain.nea.ip.mapper.ExchangeMapper;
import com.jackrain.nea.ip.mapper.ShopMapper;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.ip.services.lock.CpCShopService;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.model.enums.ExchangeOccupancyOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.st.api.ShopStrategyQueryServiceCmd;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * @Discription: Created with IntelliJ IDEA.
 * <AUTHOR>
 * @Date 创建时间：2019/4/9 15:56
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.4.0", group = "ip")
public class ExchangeAgreeCmdImpl extends CommandAdapter implements ExchangeAgreeCmd {

	@Autowired
	private CpCShopService cpCShopService;

    @DubboReference(group = "cp-ext", version = "1.0")
    private GeneralOrganizationCmd generalOrganizationCmd;

    @DubboReference(group = "st", version = "1.0")
    ShopStrategyQueryServiceCmd shopStrategyQueryServiceCmd;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        System.out.println("========同意换货服务========");
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        ExchangeMapper mapper = ApplicationContextHandle.getBean(ExchangeMapper.class);

        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！"));
        }
        System.out.println("param====>" + param.toJSONString());
        boolean isIDS = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");

        if (!(isIDS || isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！"));
        }

        if (isIDS && isObjId) {
            throw new NDSException(Resources.getMessage("参数格式错误！"));
        }

        if (isIDS && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择需要同意的换货单！"));
        }

        String tableName = param.getString("table");

        if (StringUtils.isEmpty(tableName)) {
            throw new NDSException(Resources.getMessage("表名为空"));
        }
        tableName = tableName.toLowerCase();

        if (isIDS) {
            //列表批量处理
            vh = batchAgree(param, mapper, tableName);
        } else {
            //单对象界面处理
            Long objid = param.getLongValue("objid");
            JSONObject jsonObject;
            try {
                jsonObject = separateAgree(objid, mapper, tableName);
                vh.put("code", TypeUtils.castToInt(jsonObject.get("code")));
                vh.put("message", Resources.getMessage(jsonObject.getString("message")));
            } catch (Exception e) {
                e.printStackTrace();
                vh.put("code", -1);
                vh.put("message", Resources.getMessage(e.getMessage()));
            }
        }

        return vh;
    }


    private JSONObject separateAgree(Long objid, ExchangeMapper mapper, String tableName) throws Exception {
        ExchangeAgreeCmdImpl exchangeAgreeCmd = ApplicationContextHandle.getBean(ExchangeAgreeCmdImpl.class);
        JSONObject jsonObject = new JSONObject();
        JSONObject whereKeys = new JSONObject();
        String uniqueKey = (String) TableUtil.getGetMethod(new TableUtil(), tableName);
        if (StringUtils.isEmpty(uniqueKey)) {

            jsonObject.put("message", Resources.getMessage("同意换货失败"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        uniqueKey = uniqueKey.toUpperCase();
        System.out.println("tableName====>" + tableName + "，uniqueKey======>" + uniqueKey);
        whereKeys.put("ID", objid);
        String[] returnField = {uniqueKey};
        JSONObject search = ElasticSearchUtil.search(tableName, tableName, whereKeys, null, null, 10, 0, returnField);
        JSONArray searchJSONArray = search.getJSONArray("data");
        System.out.println("es data==========>" + searchJSONArray);
        if (searchJSONArray == null || searchJSONArray.isEmpty()) {
            jsonObject.put("message", Resources.getMessage("同意换货失败"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        JSONObject object = searchJSONArray.getJSONObject(0);
        Object uniqueValue = object.get(uniqueKey);
        if (uniqueValue == null) {
            jsonObject.put("message", Resources.getMessage("同意换货失败"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        System.out.println("uniqueValue=========>" + uniqueValue.toString());
        CommonMapper commonMapper = ApplicationContextHandle.getBean(CommonMapper.class);
        HashMap<String, Object> map = commonMapper.selectAllByUniqueKey(tableName, uniqueKey, uniqueValue);
        //查询状态
        log.debug("同意换货查询map"+map.toString());
        //查询状态
        String status = "";
        if (!ObjectUtils.isEmpty(map.get("status"))){
            status = (String) map.get("status");
        }else if (!ObjectUtils.isEmpty(map.get("STATUS"))){
            status = (String) map.get("STATUS");
        }
        //判断是否满足
        Boolean isTrue = isSatisfy(status);
        if (!isTrue) {
            jsonObject.put("message", Resources.getMessage("换货状态不满足，不能同意换货！"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }

        Integer occupancyStatus = (Integer) map.get("occupancy_status");
        if (occupancyStatus.toString().equals("1")){
            jsonObject.put("message", Resources.getMessage("该换货单缺货，不允许同意换货！请点击拒绝换货按钮"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }

        if (occupancyStatus.toString().equals("0")){
            jsonObject.put("message", Resources.getMessage("该换货单占单为初始状态，不允许同意换货！"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }

        //判断价格相同
        Boolean isPrice = isSamePrice(map);
        if (!isPrice){
            jsonObject.put("message", Resources.getMessage("价格不在范围内，不同意换货！"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }

        Date now = new Date(System.currentTimeMillis());
        System.out.println("params  =====  " + "1 =" + tableName + "  2 =" + uniqueKey + "  3 =" + uniqueValue + "  4 = " + now);
        //获取sessionkey
        Long shopId = (Long) map.get("cp_c_shop_id");
        ShopMapper shopMapper = ApplicationContextHandle.getBean(ShopMapper.class);
        HashMap<String, Object> mapShopInfo = exchangeAgreeCmd.selectPlatformAndSellerNickById(shopMapper, shopId);
        if (MapUtils.isEmpty(mapShopInfo)) {
            throw new NDSException(Resources.getMessage("店铺信息有误"));
        }
        String sellerNick = TypeUtils.castToString(mapShopInfo.get("SELLER_NICK"));
        String returnAddressId = TypeUtils.castToString(mapShopInfo.get("RETURN_ADDRESS_ID")) == null ? "0" : TypeUtils.castToString(mapShopInfo.get("RETURN_ADDRESS_ID"));
        String shopSecretKey = TypeUtils.castToString(mapShopInfo.get("SHOP_SECRET_KEY"));
        if (StringUtils.isEmpty(sellerNick) || StringUtils.isEmpty(shopSecretKey)) {
            jsonObject.put("message", Resources.getMessage("店铺信息有误"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        String sessionKey = cpCShopService.getSessionKey(shopSecretKey);
        if (StringUtils.isEmpty(sessionKey)) {
            jsonObject.put("message", Resources.getMessage("店铺信息有误"));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }

        Long dispute_id = (Long) map.get("dispute_id");
        //调用同意换货接口
        ExchangeAgreeApiService exchangeAgreeApiService = ApplicationContextHandle.getBean(ExchangeAgreeApiService.class);
        ExchangeAgreeRequest exchangeAgreeRequest = new ExchangeAgreeRequest();
        exchangeAgreeRequest.setAddressId(Long.valueOf(returnAddressId));
        exchangeAgreeRequest.setSellerNick(sellerNick);
        exchangeAgreeRequest.setDisputeId(dispute_id);
        log.debug("同意换货传入参数"+JSONObject.toJSONString(exchangeAgreeRequest));
        ValueHolder exchangeAgree = exchangeAgreeApiService.exchangeAgree(exchangeAgreeRequest);
        JSONObject exchangeAgreeObj = exchangeAgree.toJSONObject();
        log.debug("同意换货返回参数"+JSONObject.toJSONString(exchangeAgree));
        if (exchangeAgreeObj.getIntValue("code") != 0) {
            mapper.updateSysRemarkByDisputeId(exchangeAgreeObj.get("message"), dispute_id, ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal());
            jsonObject.put("message", Resources.getMessage(exchangeAgreeObj.getString("message")));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        mapper.updateSysRemarkByDisputeId("同意换货处理成功", dispute_id, ExchangeOccupancyOrderStatus.OCCUPANCYEND.getVal());
        jsonObject.put("message", Resources.getMessage("同意换货成功"));
        jsonObject.put("code", 0);
        return jsonObject;
    }

    private Boolean isSamePrice(HashMap<String, Object> map) {
        Long disputeId = (Long)map.get("dispute_id");
        String[] returnFields = new String[]{"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TB_DISPUTE_ID", disputeId);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null,
                null, 50, 0, returnFields);
        if (log.isDebugEnabled()) {
            log.debug("SelectOrderExchange.DisputeId={};Result={}",disputeId, search.toString());
        }
        JSONArray data = search.getJSONArray("data");

        Long returnOrderId = 0L;
        if (CollectionUtils.isNotEmpty(data)) {
            JSONObject jsonObject1 = data.getJSONObject(0);
            returnOrderId = jsonObject1.getLong("ID");
        }
        if (returnOrderId == 0) {
            log.debug("查询退换货单失败:"+disputeId);
            throw new NDSException("查询退换货单失败");
        }

        //查询明细
       /* OcBReturnOrderExchangeMapper commonMapper = ApplicationContextHandle.getBean(OcBReturnOrderExchangeMapper.class);
        //HashMap<String, Object> exchangeItemMap = commonMapper.selectAllByUniqueKey("OC_B_RETURN_ORDER_EXCHANGE", "OC_B_RETURN_ORDER_ID", returnOrderId);
        List<OcBReturnOrderExchangeModel> ocBReturnOrderExchangeModels = commonMapper.selectReturnOrderExchangeByReturnId(returnOrderId);
        if (CollectionUtils.isEmpty(ocBReturnOrderExchangeModels)){
            throw new NDSException("查询退换货单明细失败");
        }*/
        //查询中间表金额
        //OcBReturnOrderExchangeModel ocBReturnOrderExchange = ocBReturnOrderExchangeModels.get(0);

        String exchangeSku = (String)map.get("exchange_sku");
        IpCTaobaoProductItem exchangeTaobaoProductItem = selectIpCTaobaoProductItemBySkuId(exchangeSku);
        if (null == exchangeTaobaoProductItem){
            throw new NDSException("找不到淘宝中间表商品价格");
        }

        String boughtSku = (String)map.get("bought_sku");
        IpCTaobaoProductItem boughtSkuTaobaoProductItem = selectIpCTaobaoProductItemBySkuId(boughtSku);
        if (null == boughtSkuTaobaoProductItem){
            throw new NDSException("找不到淘宝中间表商品价格!");
        }

        //查询所有店铺策略
        List<StCExchangeStrategyOrderDO> stCExchangeOrderDOList = shopStrategyQueryServiceCmd.queryAllExchangeShopStrategy();
        if (CollectionUtils.isEmpty(stCExchangeOrderDOList)){
            log.debug("换货待处理没有查询到店铺策略");
            return false;
        }
        Long shopId = (Long)map.get("cp_c_shop_id");
        List<StCExchangeStrategyOrderDO> list = stCExchangeOrderDOList.stream().filter(x -> null != x.getCpCShopId()
                && x.getCpCShopId().equals(shopId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)){
            return false;
        }
        StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO = list.get(0);
        //价格在范围内
        BigDecimal amt = stCExchangeStrategyOrderDO.getDeviationAmtAgree() == null ? BigDecimal.ZERO : stCExchangeStrategyOrderDO.getDeviationAmtAgree();
        BigDecimal abs = exchangeTaobaoProductItem.getPrice().subtract(boughtSkuTaobaoProductItem.getPrice()).abs();
        if (abs.compareTo(BigDecimal.ZERO) == 0){
            return true;
        } else if (abs.abs().compareTo(amt) < 0){
            return true;
        } else {
            return false;
        }
    }

    public <T> T copyBean(T oldBean, T newBean) {
        if (null != oldBean) {
            BeanUtils.copyProperties(oldBean, newBean);
            return newBean;
        }
        return null;
    }

    public IpCTaobaoProductItem selectIpCTaobaoProductItemBySkuId(String skuId) {
        IpCTaobaoProductItem ipCTaobaoProductItem = new IpCTaobaoProductItem();
        try {
            if (log.isDebugEnabled()) {
                log.debug("CpRpcExtService.selectIpCTaobaoProductItemBySkuId入参:" + skuId);
            }
            if (null == copyBean(generalOrganizationCmd.selectIpCTaobaoProductItemBySkuId(skuId), ipCTaobaoProductItem)) {
                return null;
            }
        } catch (Exception e) {
            log.error("CpRpcExtService.selectIpCTaobaoProductItemBySkuId", e);
            throw new NDSException("CpRpcExtService.selectIpCTaobaoProductItemBySkuId:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("CpRpcExtService.selectIpCTaobaoProductItemBySkuId返回:" + JSONObject.toJSONString(ipCTaobaoProductItem));
        }
        return ipCTaobaoProductItem;
    }

    private ValueHolder batchAgree(JSONObject param, ExchangeMapper mapper, String tableName) {

        ValueHolder valueHolder = new ValueHolder();
        int success = 0;
        int fail = 0;
        Object[] ids = param.getJSONArray("ids").toArray();
        JSONArray listArray = new JSONArray();
        String message = "";
        for (Object id : ids) {
            Long objid = TypeUtils.castToLong(id);
            JSONObject retJson;
            try {
                retJson = separateAgree(objid, mapper, tableName);
            } catch (Exception e) {
                log.debug("换货同意异常",e);
                e.printStackTrace();
                fail++;
                continue;
            }
            if (retJson.containsKey("code") && retJson.getInteger("code") == 0) {
                success++;
            } else {
                fail++;
                listArray.add(retJson);
            }
            message = message + " "+ Resources.getMessage(retJson.getString("message"));

        }

        valueHolder.put("data", listArray);
        if (0 == fail) {
            valueHolder.put("code", 0);
            valueHolder.put("message", Resources.getMessage("同意换货成功的记录数：" + success + ",失败的记录数：" + fail));
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", Resources.getMessage("同意换货成功的记录数：" + success + ",失败的记录数：" + fail));
        }
        return valueHolder;
    }

    /**
     * 判断是否满足条件
     *
     * @param status
     * @return
     */
    private Boolean isSatisfy(String status) {
        if (status.indexOf("待买家退货") == -1 && status.indexOf("买家已退货") == -1 && status.indexOf("换货关闭") == -1 && status.indexOf("换货成功") == -1 && status.indexOf("待买家修改") == -1 && status.indexOf("待发出换货商品") == -1 && status.indexOf("待买家收货") == -1 && status.indexOf("请退款") == -1) {
            return true;
        } else {
            return false;
        }
    }

    @TargetDataSource(name = "mysql")
    public HashMap<String, Object> selectPlatformAndSellerNickById(ShopMapper shopMapper, long shopId) {
        return shopMapper.selectPlatformAndSellerNickById(shopId);
    }

}
