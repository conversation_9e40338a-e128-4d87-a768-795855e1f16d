package com.jackrain.nea.ip.services.others;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.api.others.YangYiCmd;
import com.jackrain.nea.ip.common.HttpClientUtils;
import com.jackrain.nea.ip.model.others.YangYiCreateCallBlackRequest;
import com.jackrain.nea.ip.rpc.RpcOcDrpService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：Created in 17:21 2020/8/17 description ：
 * @ Modified By：
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.4.0", group = "ip")
public class YangYiCmdImpl implements YangYiCmd {
    @Autowired
    private RpcOcDrpService rpcOcDrpService;

    @Autowired
    private PropertiesConf pconf;

    @Override
    public ValueHolderV14 executeRequest(String param, String method) {
        ValueHolderV14 vh = new ValueHolderV14();
        User user = SystemUserResource.getRootUser();
        if (log.isDebugEnabled()) {
            log.debug("YangYiCmdImpl.executeRequest.param：{}，method：{}", param, method);
        }
        if (StringUtils.isBlank(param)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请求参数为空，请检查参数后重试！", user.getLocale()));
            return vh;
        }

        if (StringUtils.isBlank(method)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("[method]参数为空，请检查参数后重试！", user.getLocale()));
            return vh;
        }

        switch (method) {
            case "stockout.create":
                vh = rpcOcDrpService.saleOrderSave(param);
                break;
            case "entryorder.create":
                vh = rpcOcDrpService.saleRefundOrderSave(param);
                break;
            case "order.cancel":
                vh = rpcOcDrpService.saleOrderOrRefundCancel(param);
                break;
            default:
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("[method]参数错误，请检查参数后重试！", user.getLocale()));
                break;
        }
        if (log.isDebugEnabled()) {
            log.debug("YangYiCmdImpl.executeRequest.result：{}", vh.toJSONObject());
        }
        return vh;
    }

    @Override
    public ValueHolderV14 createCallBack(YangYiCreateCallBlackRequest request, String method, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("YangYiCmdImpl.createCallBack.param：{}，method：{}", request, method);
        }

        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.FAIL);

        String url = pconf.getProperty("r3.oms.yangyi.api.url");

        if (StringUtils.isBlank(url)) {
            valueHolderV14.setMessage("r3.oms.yangyi.api.url未配置！");
            return valueHolderV14;
        }

        if (operateUser == null) {
            valueHolderV14.setMessage("操作人不能为空！");
            return valueHolderV14;
        }

        if (request == null) {
            valueHolderV14.setMessage(Resources.getMessage("YangYiCreateCallBlackRequest参数不能为空，请检查参数后重试！", operateUser.getLocale()));
            return valueHolderV14;
        }

        if (StringUtils.isBlank(method)) {
            valueHolderV14.setMessage(Resources.getMessage("method参数不能为空，请检查参数后重试！！", operateUser.getLocale()));
            return valueHolderV14;
        }

        try {
            Map<String, String> headers = new HashMap<>(5);
            headers.put("Content-Type", "application/json");
            url = url + "?method=" + method;
            String resultStr = HttpClientUtils.sendHttpPost(url, JSON.toJSONString(request), headers);
            if (log.isDebugEnabled()) {
                log.debug("YangYiCmdImpl.confirm.sendHttpPost.resultStr：{}，method：{}", resultStr, method);
            }
            if (StringUtils.isBlank(resultStr)) {
                valueHolderV14.setMessage(Resources.getMessage("接口返回值为空！", operateUser.getLocale()));
                return valueHolderV14;
            }
            valueHolderV14 = JSON.parseObject(resultStr, ValueHolderV14.class);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("YangYiCmdImpl.confirm.error", e);
            }
            valueHolderV14.setMessage("YangYiCmdImpl.confirm.error：" + e.getMessage());
        }

        return valueHolderV14;
    }
}
