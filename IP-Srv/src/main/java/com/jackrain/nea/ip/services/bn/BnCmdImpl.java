package com.jackrain.nea.ip.services.bn;

import com.jackrain.nea.ip.api.bn.BnCmd;
import com.jackrain.nea.ip.service.bn.BnService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @ClassName BnCmdImpl
 * @Description 班牛对接接口
 * <AUTHOR>
 * @Date 2024/11/13 09:26
 * @Version 1.0
 */
@Component
@Slf4j
@DubboService(protocol = "dubbo", validation = "true", version = "1.4.0", group = "ip")
public class BnCmdImpl implements BnCmd {

    @Autowired
    private BnService bnService;

    @Override
    public ValueHolderV14 queryColumnList(Long projectId) {
        return bnService.queryColumnList(projectId);
    }

    @Override
    public ValueHolderV14 pushTask(Map<String, String> content, String projectId, String appId, String userId) {
        return bnService.pushTask(content, projectId, appId, userId);
    }

    @Override
    public ValueHolderV14 batchPushTask(List<Map<String, String>> contents, String projectId, String appId, String userId) {
        return bnService.batchPushTask(contents, projectId, appId, userId);
    }

    @Override
    public ValueHolderV14 uploadFile(String url, String name, String type) {
        return bnService.uploadFile(url, name, type);
    }
}
