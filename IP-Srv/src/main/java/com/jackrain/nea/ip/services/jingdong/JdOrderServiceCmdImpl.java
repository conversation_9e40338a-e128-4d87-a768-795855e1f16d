package com.jackrain.nea.ip.services.jingdong;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jackrain.nea.apiservice.service.jd.JdApiService;
import com.jackrain.nea.ip.api.jingdong.JdOrderServiceCmd;
import com.jackrain.nea.ip.model.jingdong.ReceiveOrderInterceptRequest;
import com.jackrain.nea.ip.model.jingdong.ReceiveRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jd.open.api.sdk.request.etms.LdopReceiveOrderInterceptRequest;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.jd.open.api.sdk.request.order.PopOrderOrderSplitCommitXmlApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:京东物流接单接口
 *
 * <AUTHOR> 孙继东
 * @since : 2019-05-20 create at : 2019-05-20 12:43
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.4.0", group = "ip")
public class JdOrderServiceCmdImpl implements JdOrderServiceCmd {
    @Autowired
    private JdApiService jdApiService;

    @Override
    public ValueHolderV14 receive(ReceiveRequest request, String sellerNick) {
        log.debug("JdOrderServiceCmdImpl.receive京东电子面单入参：" + request + ",卖家昵称：" + sellerNick);
        LdopWaybillReceiveRequest receiveRequest = JSON.parseObject(JSON.toJSONString(request), new TypeReference<LdopWaybillReceiveRequest>() {
        });
        ValueHolderV14 receive = jdApiService.receive(receiveRequest, sellerNick);
        log.debug("JdOrderServiceCmdImpl.receive京东电子面单出参：" + receive);
        return receive;
    }

    @Override
    public ValueHolderV14<String> orderSpit(String param, String sellerNick) {
        log.info("JdOrderServiceCmdImpl.orderSpit入参，param：" + param + "，sellerNick：" + sellerNick);
        PopOrderOrderSplitCommitXmlApiRequest request = new PopOrderOrderSplitCommitXmlApiRequest();
        request.setParam(param);
        ValueHolderV14<String> vh = jdApiService.orderSplit(request, sellerNick);
        log.info("JdOrderServiceCmdImpl.orderSpit出参：" + vh);
        return vh;
    }

    @Override
    public ValueHolderV14 receiveOrderIntercept(ReceiveOrderInterceptRequest request, String sellerNick) {
        log.info("JdOrderServiceCmdImpl.receiveOrderIntercept入参，request：" + request + "，sellerNick：" + sellerNick);
        LdopReceiveOrderInterceptRequest orderInterceptRequest = new LdopReceiveOrderInterceptRequest();
        orderInterceptRequest.setVendorCode(request.getVendorCode());
        orderInterceptRequest.setCancelOperator(request.getCancelOperator());
        orderInterceptRequest.setCancelOperatorCodeType(request.getCancelOperatorCodeType());
        orderInterceptRequest.setCancelTime(request.getCancelTime());
        orderInterceptRequest.setDeliveryId(request.getDeliveryId());
        orderInterceptRequest.setInterceptReason(request.getInterceptReason());
        ValueHolderV14 vh = jdApiService.receiveOrderIntercept(orderInterceptRequest, sellerNick);
        log.info("JdOrderServiceCmdImpl.receiveOrderIntercept出参：" + vh);
        return vh;
    }
}
