package com.jackrain.nea.ip.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.util.TypeUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.TransferJdOrderCmd;
import com.jackrain.nea.ip.mapper.CommonMapper;
import com.jackrain.nea.ip.rpc.RpcOcCoreService;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import java.sql.Timestamp;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> date ：Created in 15:58 2019/5/15 description ：
 * @ Modified By：
 */
@Slf4j
@Component
@DubboService(protocol = "dubbo", validation = "true", version = "1.4.0", group = "ip")
public class TransferJdOrderCmdImpl extends CommandAdapter implements TransferJdOrderCmd {
    @Autowired
    private RpcOcCoreService rpcOcCoreService;
    @Autowired
    private IpBOrderTjxtLogSaveCmdImpl ipBOrderLogSaveCmdImpl;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        log.info("======京东订单转换服务=========");
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        log.info("param======>" + param.toString());

        boolean isIDS = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");

        if (!(isIDS || isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && isObjId) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择需要单据转换的记录！", querySession.getLocale()));
        }

        String tableName = param.getString("table");

        if (StringUtils.isEmpty(tableName)) {
            throw new NDSException(Resources.getMessage("表名为空", querySession.getLocale()));
        }

        tableName = tableName.toLowerCase();

        if (isIDS) {
            //列表批量处理
            vh = batchMarked(param, tableName, querySession);
        } else {
            //单对象界面处理
            Long objid = param.getLongValue("objid");
            JSONObject jsonObject;
            try {
                jsonObject = separateMarked(objid, tableName, querySession);
                vh.put("code", TypeUtils.castToInt(jsonObject.get("code")));
                vh.put("message", Resources.getMessage(jsonObject.getString("message"), querySession.getLocale()));
            } catch (Exception e) {
                e.printStackTrace();
                vh.put("code", -1);
                vh.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
            }
        }
        return vh;
    }

    private JSONObject separateMarked(Long objid, String tableName, QuerySession querySession) throws Exception {
        String uniqueKey = "ORDER_ID";
        JSONObject jsonObject = new JSONObject();
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ID", objid);
        String[] returnField = {uniqueKey};
        JSONObject search = ElasticSearchUtil.search(tableName, tableName, whereKeys, null, null, 10, 0, returnField);
        JSONArray searchJSONArray = search.getJSONArray("data");
        log.info("es data==========>" + searchJSONArray);
        if (searchJSONArray == null || searchJSONArray.isEmpty()) {
            jsonObject.put("message", Resources.getMessage("单据转换失败！", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        JSONObject object = searchJSONArray.getJSONObject(0);
        String uniqueValue = TypeUtils.castToString(object.get(uniqueKey));
        if (StringUtils.isEmpty(uniqueValue)) {
            jsonObject.put("message", Resources.getMessage("单据转换失败！", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        log.info("uniqueValue=========>" + uniqueValue);
        CommonMapper commonMapper = ApplicationContextHandle.getBean(CommonMapper.class);
        HashMap<String, Object> map = commonMapper.selectIstransByUniqueKey(tableName, uniqueKey, uniqueValue);
        if (MapUtils.isEmpty(map)) {
            jsonObject.put("message", Resources.getMessage("单据转换失败！", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        int istrans = Tools.getInt(map.get("istrans"), 0);
        if (istrans != 0) {
            jsonObject.put("message", Resources.getMessage("状态非未转换不允许单据转换！", querySession.getLocale()));
            jsonObject.put("code", -1);
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        JSONObject retObj = rpcOcCoreService.transferJdOrder(uniqueValue, ChannelType.JINGDONG, querySession.getUser());

        if (retObj.getIntValue("code") != 0) {
            ipBOrderLogSaveCmdImpl.addchangeLog(querySession,"IP_B_JINGDONG_ORDER",new Timestamp(System.currentTimeMillis()),objid,"转换状态","未转换","转换异常");
            retObj.put("objid", objid);
            return retObj;
        }else {
            ipBOrderLogSaveCmdImpl.addchangeLog(querySession,"IP_B_JINGDONG_ORDER",new Timestamp(System.currentTimeMillis()),objid,"转换状态","未转换","已转换");
            return retObj;
        }
    }

    private ValueHolder batchMarked(JSONObject param, String tableName, QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        int success = 0;
        int fail = 0;
        Object[] ids = param.getJSONArray("ids").toArray();
        JSONArray listArray = new JSONArray();
        for (Object id : ids) {
            Long objid = TypeUtils.castToLong(id);
            JSONObject retJson;
            try {
                retJson = separateMarked(objid, tableName, querySession);
            } catch (Exception e) {
                e.printStackTrace();
                retJson = new JSONObject();
                retJson.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
                retJson.put("code", -1);
                retJson.put("objid", objid);
                listArray.add(retJson);
                fail++;
                continue;
            }
            if (retJson.containsKey("code") && retJson.getInteger("code") == 0) {
                success++;
            } else {
                fail++;
                listArray.add(retJson);
            }
        }

        valueHolder.put("data", listArray);
        if (0 == fail) {
            valueHolder.put("code", 0);
            valueHolder.put("message", Resources.getMessage("成功" + success + "条！", querySession.getLocale()));
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", Resources.getMessage("成功" + success + "条，失败" + fail + "条！", querySession.getLocale()));
        }
        return valueHolder;
    }
}
